# 锦湖日丽 AI 平台

基于 React + TypeScript + Tailwind CSS 构建的现代化 AI 对话平台，提供智能对话、文档生成、代码辅助等功能。

## 🌟 特性

- **🎨 现代化设计** - 采用深色主题，类似 Claude/ChatGPT 的专业界面
- **💬 智能对话** - 支持实时 AI 对话，提供丰富的交互体验
- **📱 响应式布局** - 完美适配桌面端和移动端
- **🔧 可折叠侧边栏** - 灵活的界面布局，支持侧边栏展开/收起
- **💾 对话历史** - 自动保存和管理历史对话记录
- **⚡ 快速输入** - 支持多行输入、快捷键操作
- **🎯 预设提示** - 提供常用提示词模板，快速开始对话

## 🛠️ 技术栈

- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全的开发体验
- **Tailwind CSS** - 原子化 CSS 框架
- **React Hooks** - 现代化状态管理
- **Jest + React Testing Library** - 完整的测试解决方案

## 📁 项目结构

```
src/
├── components/           # 可复用组件
│   ├── Button.tsx       # 按钮组件
│   ├── Card.tsx         # 卡片组件
│   ├── Header.tsx       # 头部组件
│   ├── Loading.tsx      # 加载组件
│   ├── Modal.tsx        # 模态框组件
│   ├── Sidebar.tsx      # 侧边栏组件
│   └── index.ts         # 组件导出
├── pages/               # 页面组件
│   ├── ChatInterface.tsx # 聊天界面
│   └── ComponentDemo.tsx # 组件演示
├── hooks/               # 自定义 Hooks
│   ├── useLocalStorage.ts # 本地存储 Hook
│   ├── useToggle.ts      # 切换状态 Hook
│   └── index.ts          # Hooks 导出
├── utils/               # 工具函数
├── types/               # TypeScript 类型定义
└── App.tsx              # 主应用组件
```

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm start
```

应用将在 `http://localhost:3000` 启动。

### 运行测试

```bash
npm test
```

### 构建生产版本

```bash
npm run build
```

## 🎯 主要功能

### 1. 智能对话界面
- 类似 ChatGPT 的对话体验
- 支持实时消息发送和接收
- 自动滚动到最新消息
- 加载状态指示

### 2. 侧边栏管理
- 可折叠的侧边栏设计
- 历史对话列表
- 新建对话功能
- 对话删除操作

### 3. 输入增强
- 多行文本输入
- 自动调整输入框高度
- Enter 发送，Shift+Enter 换行
- 附件上传、图片使用等扩展功能

### 4. 预设提示词
- 常用场景模板
- 一键填充输入框
- 提高用户体验

## 🔧 自定义组件

### Sidebar 组件
```tsx
<Sidebar 
  collapsed={sidebarCollapsed}
  onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
  selectedConversation={selectedConversation}
  onSelectConversation={setSelectedConversation}
/>
```

### ChatInterface 组件
```tsx
<ChatInterface />
```

## 🎨 设计系统

### 颜色主题
- **主色调**: 紫色渐变 (`from-purple-500 to-pink-500`)
- **背景色**: 深灰色 (`bg-gray-900`, `bg-gray-800`)
- **文字色**: 白色和灰色层次

### 组件规范
- 统一的圆角设计 (`rounded-lg`)
- 平滑的过渡动画 (`transition-all`)
- 一致的间距系统 (Tailwind spacing)

## �� 开发指南

### 添加新的对话功能
1. 在 `ChatInterface.tsx` 中扩展消息类型
2. 实现相应的 UI 组件
3. 添加对应的测试用例

### 自定义主题
1. 修改 `tailwind.config.js` 中的颜色配置
2. 更新组件中的相关类名
3. 确保保持设计一致性

## 🧪 测试

项目包含完整的测试覆盖：
- 组件渲染测试
- 用户交互测试
- 类型检查

## 📦 构建和部署

### 环境变量
创建 `.env` 文件：
```env
REACT_APP_API_URL=your_api_url
REACT_APP_VERSION=1.0.0
```

### 部署到生产环境
```bash
npm run build
# 将 build 文件夹部署到您的服务器
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 👥 团队

锦湖日丽 AI 平台开发团队

---

> 💡 这是一个现代化的 AI 对话平台，致力于提供最佳的用户体验和开发者友好的代码结构。 