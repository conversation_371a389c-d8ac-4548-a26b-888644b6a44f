import React, { useRef, useEffect, useState, useCallback } from 'react';
import <PERSON><PERSON><PERSON>ender<PERSON> from './MarkdownRenderer';
import MessageActions from './MessageActions';
import { DingTalkUserInfo, UploadedFileDetail } from '../../types';

export interface Message {
    id: string;
    type: 'user' | 'assistant';
    content: any;
    timestamp: Date;
    attachments?: UploadedFileDetail[];
}

interface ChatHistoryProps {
    messages: Message[];
    isLoading: boolean;
    isDarkMode: boolean;
    userInfo?: DingTalkUserInfo | null;
    isTransitioning?: boolean;
    streamingMessageId?: string | null;
    onSuggestionClick?: (suggestion: string) => void;
    onAttachmentClick?: (attachment: UploadedFileDetail) => void;
    onRegenerate?: (messageId: string) => void;
}

const SuggestionCard: React.FC<{ title: string; description: string; isDarkMode: boolean; onClick: () => void }> = ({ title, description, isDarkMode, onClick }) => (
    <button onClick={onClick} className={`p-4 rounded-lg transition-all duration-300 text-left w-full ${isDarkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-100 hover:bg-gray-200'}`}>
        <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{title}</h3>
        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>{description}</p>
    </button>
);

const ChatHistory: React.FC<ChatHistoryProps> = ({
    messages,
    isLoading,
    isDarkMode,
    userInfo,
    isTransitioning,
    streamingMessageId,
    onSuggestionClick,
    onAttachmentClick,
    onRegenerate
}) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    const [isAtBottom, setIsAtBottom] = useState(true);
    const [showScrollToBottom, setShowScrollToBottom] = useState(false);
    const [unreadCount, setUnreadCount] = useState(0);
    const prevIsLoadingRef = useRef(isLoading);

    useEffect(() => {
        // 当加载完成时（从加载中变为非加载中），自动滚动到底部
        if (prevIsLoadingRef.current && !isLoading && messages.length > 0) {
            setTimeout(() => scrollToBottom(true), 100);
        }
        prevIsLoadingRef.current = isLoading;
    }, [isLoading, messages.length]);

    // 检查是否在底部
    const checkIfAtBottom = useCallback(() => {
        if (!scrollContainerRef.current) return true;

        const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
        const threshold = 100; // 100px 的容差
        const atBottom = scrollHeight - scrollTop - clientHeight < threshold;

        setIsAtBottom(atBottom);
        setShowScrollToBottom(!atBottom && messages.length > 0);

        if (atBottom) {
            setUnreadCount(0);
        }

        return atBottom;
    }, [messages.length]);

    // 滚动到底部
    const scrollToBottom = useCallback((force = false) => {
        if (!messagesEndRef.current) return;

        if (force || isAtBottom) {
            messagesEndRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'end'
            });
            setUnreadCount(0);
        }
    }, [isAtBottom]);

    // 处理滚动事件
    const handleScroll = useCallback(() => {
        checkIfAtBottom();
    }, [checkIfAtBottom]);

    // 监听消息变化
    useEffect(() => {
        if (messages.length === 0) return;

        const wasAtBottom = checkIfAtBottom();

        if (wasAtBottom) {
            // 如果用户在底部，自动滚动到新消息
            setTimeout(() => scrollToBottom(true), 100);
        } else {
            // 如果用户不在底部，增加未读计数
            setUnreadCount(prev => prev + 1);
        }
    }, [messages, checkIfAtBottom, scrollToBottom]);

    // 监听加载状态变化
    useEffect(() => {
        if (isLoading && isAtBottom) {
            setTimeout(() => scrollToBottom(true), 100);
        }
    }, [isLoading, isAtBottom, scrollToBottom]);

    // 监听流式传输变化，自动滚动
    useEffect(() => {
        if (streamingMessageId && isAtBottom) {
            setTimeout(() => scrollToBottom(true), 50);
        }
    }, [streamingMessageId, messages, isAtBottom, scrollToBottom]);

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-900',
        textTertiary: isDarkMode ? 'text-gray-500' : 'text-gray-500',
        scrollbar: isDarkMode ? 'scrollbar-dark' : 'scrollbar-light',
    };

    if (messages.length === 0 && !isLoading) {
        return (
            <div className="flex flex-col items-center justify-center h-full text-center p-8">
                <div className="w-20 h-20 rounded-full shadow-lg mb-6 overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                    <img src="/ai_avatar.png" alt="AI Avatar" className="w-full h-full object-cover" onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const parent = target.parentElement;
                        if (parent) {
                            parent.innerHTML = '<span class="text-white font-bold text-3xl">锦</span>';
                        }
                    }} />
                </div>
                <h2 className={`text-2xl font-light mb-2 ${themeClasses.text}`}>
                    今天我能如何帮助您？
                </h2>
                <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} max-w-md mb-8`}>
                    您可以从下面的建议中获取灵感。
                </p>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full max-w-3xl">
                    <SuggestionCard
                        title="寻找创意灵感"
                        description="为我的新产品写一个响亮的口号"
                        isDarkMode={isDarkMode}
                        onClick={() => onSuggestionClick?.("为我的新产品写一个响亮的口号")}
                    />
                    <SuggestionCard
                        title="分析复杂数据"
                        description="总结一下这份销售报告的主要趋势"
                        isDarkMode={isDarkMode}
                        onClick={() => onSuggestionClick?.("总结一下这份销售报告的主要趋势")}
                    />
                    <SuggestionCard
                        title="请求代码帮助"
                        description="用 Python 写一个函数来反转字符串"
                        isDarkMode={isDarkMode}
                        onClick={() => onSuggestionClick?.("用 Python 写一个函数来反转字符串")}
                    />
                    <SuggestionCard
                        title="规划一次旅行"
                        description="帮我制定一个为期三天的北京旅游计划"
                        isDarkMode={isDarkMode}
                        onClick={() => onSuggestionClick?.("帮我制定一个为期三天的北京旅游计划")}
                    />
                </div>
            </div>
        );
    }

    return (
        <div className="h-full flex flex-col relative">
            {/* 消息列表 - 使用全部可用高度并内部滚动 */}
            <div
                ref={scrollContainerRef}
                onScroll={handleScroll}
                className={`h-full overflow-y-auto px-8 py-8 ${themeClasses.scrollbar}`}
                style={{
                    scrollBehavior: 'smooth'
                }}
            >
                <div className="max-w-4xl mx-auto space-y-8">
                    {messages.map((message, index) => {
                        const isStreaming = streamingMessageId === message.id;

                        return (
                            <div
                                key={message.id}
                                className="flex space-x-4 group"
                            >
                                {/* 头像 */}
                                <div className="flex-shrink-0">
                                    {message.type === 'user' ? (
                                        <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-cyan-500 rounded-full flex items-center justify-center shadow-md">
                                            {userInfo?.avatar ? (
                                                <img src={userInfo.avatar} alt={userInfo.actualName} className="w-full h-full rounded-full object-cover" />
                                            ) : (
                                                <span className="text-white text-sm font-medium">
                                                    {userInfo?.actualName ? userInfo.actualName.charAt(0) : '您'}
                                                </span>
                                            )}
                                        </div>
                                    ) : (
                                        <div
                                            className={`w-10 h-10 rounded-full shadow-md overflow-hidden ${isStreaming ? 'animate-pulse' : ''}`}
                                        >
                                            <img
                                                src="/ai_avatar.png"
                                                alt="AI头像"
                                                className="w-full h-full object-cover"
                                                onError={(e) => {
                                                    // 如果图片加载失败，回退到原来的渐变背景
                                                    const target = e.target as HTMLImageElement;
                                                    target.style.display = 'none';
                                                    const parent = target.parentElement;
                                                    if (parent) {
                                                        parent.className = `w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-md ${isStreaming ? 'animate-pulse' : ''}`;
                                                        parent.innerHTML = '<span class="text-white font-bold text-lg">锦</span>';
                                                    }
                                                }}
                                            />
                                        </div>
                                    )}
                                </div>

                                {/* 消息内容 */}
                                <div className="flex-1 min-w-0">
                                    <div className={`${themeClasses.text} ${message.type === 'user' ? 'whitespace-pre-wrap' : ''} break-words leading-relaxed text-base relative ${message.type === 'assistant' ? `p-4 rounded-2xl ${isDarkMode ? 'bg-gray-800/30' : 'bg-gray-50/50'}` : ''}`}>
                                        {message.type === 'assistant' ? (
                                            <MarkdownRenderer
                                                content={message.content}
                                                isDarkMode={isDarkMode}
                                                isStreaming={isStreaming}
                                            />
                                        ) : (
                                            <>
                                                {typeof message.content === 'string' && message.content}
                                                {Array.isArray(message.content) && (
                                                    <div className="space-y-2">
                                                        {message.content.map((part, i) => {
                                                            if (part.type === 'text') {
                                                                return <p key={i} className="whitespace-pre-wrap">{part.text}</p>;
                                                            }
                                                            if (part.type === 'image_url' && part.image_url.url) {
                                                                return <img key={i} src={part.image_url.url} alt="User upload" className="max-w-xs rounded-lg shadow-md" />;
                                                            }
                                                            return null;
                                                        })}
                                                    </div>
                                                )}
                                                {message.attachments && message.attachments.length > 0 && (
                                                    <div className="mt-4">
                                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                                            {message.attachments.map((att) => (
                                                                <button
                                                                    key={att.id}
                                                                    onClick={() => onAttachmentClick?.(att)}
                                                                    className={`flex items-center space-x-2 p-2 rounded-lg transition-colors cursor-pointer ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'}`}
                                                                >
                                                                    <svg className="w-5 h-5 flex-shrink-0 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
                                                                    <span className={`text-sm truncate ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>{att.originalFileName}</span>
                                                                </button>
                                                            ))}
                                                        </div>
                                                    </div>
                                                )}
                                                {/* 流式传输时的光标效果 */}
                                                {isStreaming && (
                                                    <span className="inline-block w-0.5 h-5 bg-purple-500 ml-1 animate-pulse"></span>
                                                )}
                                            </>
                                        )}
                                    </div>

                                    {/* AI消息的操作按钮 - 仅在非流式传输状态下显示 */}
                                    {message.type === 'assistant' && !isStreaming && (
                                        <MessageActions
                                            content={message.content}
                                            isDarkMode={isDarkMode}
                                            messageId={message.id}
                                            onRegenerate={onRegenerate}
                                        />
                                    )}

                                    <div className={`${themeClasses.textTertiary} text-xs mt-2 flex items-center space-x-2`}>
                                        <span>
                                            {message.timestamp.toLocaleTimeString('zh-CN', {
                                                hour: '2-digit',
                                                minute: '2-digit'
                                            })}
                                        </span>
                                        {/* 流式传输状态指示器 */}
                                        {isStreaming && (
                                            <span className="flex items-center space-x-1 text-purple-500">
                                                <div className="w-1 h-1 bg-purple-500 rounded-full animate-bounce"></div>
                                                <div className="w-1 h-1 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                                <div className="w-1 h-1 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                                <span className="text-xs ml-1">正在输入...</span>
                                            </span>
                                        )}
                                    </div>
                                </div>
                            </div>
                        );
                    })}

                    {/* 加载中状态 */}
                    {isLoading && !streamingMessageId && (
                        <div className="flex space-x-4">
                            <div className="w-10 h-10 rounded-full shadow-md overflow-hidden">
                                <img
                                    src="/ai_avatar.png"
                                    alt="AI头像"
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                        // 如果图片加载失败，回退到原来的渐变背景
                                        const target = e.target as HTMLImageElement;
                                        target.style.display = 'none';
                                        const parent = target.parentElement;
                                        if (parent) {
                                            parent.className = 'w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-md';
                                            parent.innerHTML = '<span class="text-white font-bold text-lg">锦</span>';
                                        }
                                    }}
                                />
                            </div>
                            <div className="flex-1">
                                <div className={`p-4 rounded-2xl ${isDarkMode ? 'bg-gray-800/30' : 'bg-gray-50/50'}`}>
                                    <div className={`flex items-center space-x-3 ${themeClasses.textTertiary}`}>
                                        <div className="flex space-x-1">
                                            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                                            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                        </div>
                                        <span className="text-sm">AI 正在思考中...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    <div ref={messagesEndRef} />
                </div>
            </div>

            {/* 滚动到底部按钮 */}
            {showScrollToBottom && (
                <div className="absolute bottom-4 right-8 z-10">
                    <button
                        onClick={() => scrollToBottom(true)}
                        className={`flex items-center space-x-2 px-4 py-2 ${isDarkMode ? 'bg-gray-800 hover:bg-gray-700 text-white' : 'bg-white hover:bg-gray-50 text-gray-900'
                            } rounded-full shadow-lg border ${isDarkMode ? 'border-gray-700' : 'border-gray-200'
                            } transition-all duration-200 hover:scale-105`}
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                        <span className="text-sm font-medium">
                            {unreadCount > 0 ? `${unreadCount} 条新消息` : '回到底部'}
                        </span>
                    </button>
                </div>
            )}
        </div>
    );
};

export default ChatHistory; 