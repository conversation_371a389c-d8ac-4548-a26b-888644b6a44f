import React, { useState, useEffect, useRef, useMemo } from 'react';
import { ModelData } from '../../utils/api';

interface ModelSelectorProps {
    models: ModelData[];
    selectedModelId: string;
    onModelSelect: (modelId: string) => void;
    isDarkMode: boolean;
    disabled: boolean;
}

// 辅助函数：根据提供商分组模型
const groupModelsByProvider = (models: ModelData[]) => {
    return models.reduce((acc, model) => {
        const providerName = model.targetModel.provider.providerName;
        if (!acc[providerName]) {
            acc[providerName] = [];
        }
        acc[providerName].push(model);
        return acc;
    }, {} as Record<string, ModelData[]>);
};

const ModelSelector: React.FC<ModelSelectorProps> = ({ models, selectedModelId, onModelSelect, isDarkMode, disabled }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const dropdownRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);

    // 只显示 CHAT 类型的模型
    const chatModels = useMemo(() => {
        return models.filter(model => model.targetModel.modelType === 'CHAT');
    }, [models]);

    const selectedModel = chatModels.find(m => m.requestModel === selectedModelId);

    const filteredModels = useMemo(() => {
        if (!searchTerm) {
            return chatModels;
        }
        return chatModels.filter(model =>
            model.requestModel.toLowerCase().includes(searchTerm.toLowerCase()) ||
            model.targetModel.modelName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            model.targetModel.provider.providerName.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }, [chatModels, searchTerm]);

    const groupedModels = useMemo(() => groupModelsByProvider(filteredModels), [filteredModels]);

    useEffect(() => {
        if (isOpen) {
            searchInputRef.current?.focus();
        } else {
            // 关闭下拉框时重置搜索词
            setSearchTerm('');
        }
    }, [isOpen]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const themeClasses = {
        bg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        text: isDarkMode ? 'text-gray-200' : 'text-gray-800',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-500',
        border: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        hoverBg: isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100',
        selectedBg: isDarkMode ? 'bg-purple-600' : 'bg-purple-500',
        disabledBg: isDarkMode ? 'bg-gray-700' : 'bg-gray-200',
        disabledText: isDarkMode ? 'text-gray-500' : 'text-gray-400',
        groupHeaderText: isDarkMode ? 'text-purple-400' : 'text-purple-600',
    };

    return (
        <div className="relative w-full" ref={dropdownRef}>
            <button
                disabled={disabled}
                onClick={() => setIsOpen(!isOpen)}
                className={`w-full flex items-center justify-between px-3 py-2 text-left transition-colors duration-200 ${themeClasses.bg} border ${themeClasses.border} rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 ${disabled ? `bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed` : 'hover:border-purple-400'}`}
            >
                <div className="flex items-center truncate">
                    {selectedModel ? (
                        <>
                            <div className="truncate">
                                <span className={`block text-xs font-medium ${themeClasses.textSecondary}`}>{selectedModel.targetModel.provider.providerName}</span>
                                <span className={`block font-medium truncate ${themeClasses.text}`}>{selectedModel.requestModel}</span>
                            </div>
                        </>
                    ) : (
                        <span className={themeClasses.text}>选择一个模型</span>
                    )}
                </div>
                {!disabled && (
                    <svg className={`w-5 h-5 ml-2 flex-shrink-0 ${themeClasses.textSecondary} transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                )}
            </button>

            {isOpen && !disabled && (
                <div className={`absolute z-20 w-full mt-2 ${themeClasses.bg} border ${themeClasses.border} rounded-lg shadow-xl overflow-hidden animate-fadeIn animation-duration-200`}>
                    <div className="p-2 border-b border-gray-200 dark:border-gray-700">
                        <div className="relative">
                            <input
                                ref={searchInputRef}
                                type="text"
                                placeholder="搜索聊天模型 (e.g. gpt-4, deepseek)"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className={`w-full px-4 py-2 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'} ${themeClasses.text} border-transparent focus:border-purple-500 focus:ring-purple-500 rounded-md focus:outline-none transition-colors`}
                            />
                        </div>
                    </div>
                    <ul className="max-h-64 overflow-auto py-1">
                        {filteredModels.length > 0 ? (
                            Object.entries(groupedModels).map(([provider, modelList]) => (
                                <li key={provider}>
                                    <div className={`px-3 py-1.5 text-xs font-semibold tracking-wider uppercase ${themeClasses.groupHeaderText}`}>
                                        {provider}
                                    </div>
                                    <ul>
                                        {modelList.map(model => (
                                            <li
                                                key={model.requestModel}
                                                onClick={() => {
                                                    onModelSelect(model.requestModel);
                                                    setIsOpen(false);
                                                }}
                                                className={`flex items-center px-4 py-2 mx-2 rounded-md cursor-pointer transition-colors duration-150 ${selectedModelId === model.requestModel ? `${themeClasses.selectedBg} text-white font-medium` : `${themeClasses.text} ${themeClasses.hoverBg}`}`}
                                            >
                                                <div className="flex-1 truncate">
                                                    <span className="block truncate">{model.requestModel}</span>
                                                    <span className={`block text-xs ${selectedModelId === model.requestModel ? 'text-gray-200' : themeClasses.textSecondary}`}>
                                                        最大输出: {(model.targetModel.maxOutputTokens / 1000).toFixed(0)}K tokens
                                                    </span>
                                                </div>
                                            </li>
                                        ))}
                                    </ul>
                                </li>
                            ))
                        ) : (
                            <li className={`px-3 py-4 text-center ${themeClasses.textSecondary}`}>
                                未找到匹配的聊天模型
                            </li>
                        )}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default ModelSelector; 