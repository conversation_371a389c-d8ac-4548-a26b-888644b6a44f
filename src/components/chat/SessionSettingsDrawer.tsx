import React, { useMemo, useState, useEffect } from 'react';
import { modelsApi, ModelData } from '../../utils/api';
import { ModelSelector } from './';

interface SessionSettingsDrawerProps {
    isOpen: boolean;
    onClose: () => void;
    metadata: any;
    isDarkMode: boolean;
    onSave: (newMetadata: any) => void;
    token?: string;
}

const SessionSettingsDrawer: React.FC<SessionSettingsDrawerProps> = ({
    isOpen,
    onClose,
    metadata,
    isDarkMode,
    onSave,
    token
}) => {
    const [currentSettings, setCurrentSettings] = useState(metadata?.settings);
    const [models, setModels] = useState<ModelData[]>([]);
    const isAppSession = useMemo(() => !!metadata?.app?.id, [metadata]);

    // 获取当前选中的模型信息
    const selectedModel = useMemo(() => {
        if (!currentSettings?.model || !models.length) return null;
        return models.find(model =>
            model.targetModel.modelType === 'CHAT' &&
            model.requestModel === currentSettings.model
        );
    }, [models, currentSettings?.model]);

    // 根据选中模型动态计算最大token限制
    const maxTokensLimit = useMemo(() => {
        if (selectedModel?.targetModel?.maxOutputTokens) {
            return selectedModel.targetModel.maxOutputTokens;
        }
        return 128000; // 默认值
    }, [selectedModel]);

    useEffect(() => {
        // 当 metadata 更新时，同步更新内部 state
        setCurrentSettings(metadata?.settings);
    }, [metadata]);

    useEffect(() => {
        const fetchModels = async () => {
            try {
                const response = await modelsApi.getModels(token);
                // 只保留 CHAT 类型的模型
                const chatModels = response.data.filter(model =>
                    model.targetModel.modelType === 'CHAT'
                );
                setModels(chatModels);
            } catch (error) {
                console.error("获取模型列表失败:", error);
            }
        };
        if (isOpen) {
            fetchModels();
        }
    }, [isOpen, token]);

    const handleModelChange = (modelId: string) => {
        const newSettings = {
            ...currentSettings,
            model: modelId,
        };

        // 如果当前的maxTokens超过新模型的限制，则调整为新模型的限制
        const newModel = models.find(model => model.requestModel === modelId);
        if (newModel && currentSettings.maxTokens > newModel.targetModel.maxOutputTokens) {
            newSettings.maxTokens = newModel.targetModel.maxOutputTokens;
        }

        setCurrentSettings(newSettings);

        const newMetadata = {
            ...metadata,
            settings: newSettings,
        };
        onSave(newMetadata);
    };

    const handleTemperatureChange = (value: number) => {
        const newSettings = {
            ...currentSettings,
            temperature: value,
        };
        setCurrentSettings(newSettings);

        const newMetadata = {
            ...metadata,
            settings: newSettings,
        };
        onSave(newMetadata);
    };

    const handleMaxTokensChange = (value: number) => {
        // 确保不超过当前模型的最大输出token限制
        const clampedValue = Math.min(value, maxTokensLimit);
        const newSettings = {
            ...currentSettings,
            maxTokens: clampedValue,
        };
        setCurrentSettings(newSettings);

        const newMetadata = {
            ...metadata,
            settings: newSettings,
        };
        onSave(newMetadata);
    };

    const themeClasses = {
        bg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        text: isDarkMode ? 'text-gray-200' : 'text-gray-800',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-500',
        border: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        inputBg: isDarkMode ? 'bg-gray-700' : 'bg-gray-100',
        disabledBg: isDarkMode ? 'bg-gray-600' : 'bg-gray-200',
        disabledText: isDarkMode ? 'text-gray-400' : 'text-gray-500',
        sliderTrack: isDarkMode ? 'bg-gray-600' : 'bg-gray-300',
        sliderThumb: isDarkMode ? 'bg-blue-500' : 'bg-blue-600',
    };

    const renderContent = () => {
        if (!metadata || !currentSettings) {
            return <p className={themeClasses.textSecondary}>没有可用的会话设置。</p>;
        }

        return (
            <div className="space-y-6">
                {isAppSession ? (
                    <div>
                        <label className={`block text-sm font-medium ${themeClasses.textSecondary}`}>智能体应用</label>
                        <p className={`mt-1 text-lg font-semibold ${themeClasses.text}`}>{metadata.app.name}</p>
                        <p className={themeClasses.textSecondary}>当前会话由一个智能体应用驱动，配置已锁定。</p>
                    </div>
                ) : (
                    <div>
                        <h3 className={`text-lg font-medium ${themeClasses.text}`}>自定义设置</h3>
                        <p className={themeClasses.textSecondary}>修改当前对话的参数设置。</p>
                    </div>
                )}

                {/* 模型设置 */}
                <div>
                    <label htmlFor="model" className={`block text-sm font-medium ${themeClasses.textSecondary} mb-2`}>
                        聊天模型
                        {selectedModel && (
                            <span className={`ml-2 text-xs ${themeClasses.text} font-mono px-2 py-1 rounded ${themeClasses.inputBg}`}>
                                最大输出: {(selectedModel.targetModel.maxOutputTokens / 1000).toFixed(0)}K
                            </span>
                        )}
                    </label>
                    <ModelSelector
                        models={models}
                        selectedModelId={currentSettings.model || ''}
                        onModelSelect={handleModelChange}
                        isDarkMode={isDarkMode}
                        disabled={isAppSession}
                    />
                </div>

                {/* Temperature设置 */}
                <div>
                    <label className={`block text-sm font-medium ${themeClasses.textSecondary} mb-2`}>
                        随机性 (Temperature)
                        <span className={`ml-2 text-xs ${themeClasses.text} font-mono px-2 py-1 rounded ${themeClasses.inputBg}`}>
                            {currentSettings.temperature?.toFixed(2) || '0.20'}
                        </span>
                    </label>
                    <div className="space-y-2">
                        <input
                            type="range"
                            min="0"
                            max="1"
                            step="0.01"
                            value={currentSettings.temperature || 0.2}
                            onChange={(e) => handleTemperatureChange(parseFloat(e.target.value))}
                            disabled={isAppSession}
                            className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${isAppSession
                                ? themeClasses.disabledBg
                                : `${themeClasses.sliderTrack} slider-thumb`
                                }`}
                            style={{
                                background: isAppSession
                                    ? undefined
                                    : `linear-gradient(to right, ${isDarkMode ? '#3b82f6' : '#2563eb'} 0%, ${isDarkMode ? '#3b82f6' : '#2563eb'} ${((currentSettings.temperature || 0.2) * 100)}%, ${isDarkMode ? '#4b5563' : '#d1d5db'} ${((currentSettings.temperature || 0.2) * 100)}%, ${isDarkMode ? '#4b5563' : '#d1d5db'} 100%)`
                            }}
                        />
                        <div className={`flex justify-between text-xs ${themeClasses.textSecondary}`}>
                            <span>0.00 (更精确)</span>
                            <span>1.00 (更创造性)</span>
                        </div>
                    </div>
                </div>

                {/* Max Tokens设置 */}
                <div>
                    <label className={`block text-sm font-medium ${themeClasses.textSecondary} mb-2`}>
                        最大输出长度 (Max Output Tokens)
                        <span className={`ml-2 text-xs ${themeClasses.text} font-mono px-2 py-1 rounded ${themeClasses.inputBg}`}>
                            {maxTokensLimit ? `限制: ${(maxTokensLimit / 1000).toFixed(0)}K` : ''}
                        </span>
                    </label>
                    <div className="space-y-2">
                        <input
                            type="number"
                            min="1000"
                            max={maxTokensLimit}
                            step="1000"
                            value={currentSettings.maxTokens || Math.min(128000, maxTokensLimit)}
                            onChange={(e) => handleMaxTokensChange(parseInt(e.target.value) || Math.min(128000, maxTokensLimit))}
                            disabled={isAppSession}
                            className={`w-full px-3 py-2 rounded-md border text-sm ${isAppSession
                                ? `${themeClasses.disabledBg} ${themeClasses.disabledText} cursor-not-allowed`
                                : `${themeClasses.inputBg} ${themeClasses.text} ${themeClasses.border} focus:ring-2 focus:ring-blue-500 focus:border-transparent`
                                }`}
                        />
                        <div className={`flex justify-between text-xs ${themeClasses.textSecondary}`}>
                            <span>建议范围: 1K - {(maxTokensLimit / 1000).toFixed(0)}K</span>
                            <span>当前: {((currentSettings.maxTokens || Math.min(128000, maxTokensLimit)) / 1000).toFixed(0)}K</span>
                        </div>
                        {selectedModel && (
                            <div className={`text-xs ${themeClasses.textSecondary} p-2 rounded ${themeClasses.inputBg}`}>
                                <strong>模型信息:</strong> {selectedModel.targetModel.modelName}
                                | 上下文窗口: {(selectedModel.targetModel.contextWindow / 1000).toFixed(0)}K
                                | 最大输出: {(selectedModel.targetModel.maxOutputTokens / 1000).toFixed(0)}K
                            </div>
                        )}
                    </div>
                </div>

                {/* 设置说明 */}
                <div className={`p-4 rounded-lg ${themeClasses.inputBg} space-y-2`}>
                    <h4 className={`text-sm font-medium ${themeClasses.text}`}>参数说明</h4>
                    <div className={`text-xs ${themeClasses.textSecondary} space-y-1`}>
                        <p><strong>聊天模型</strong>: 选择用于对话的AI模型。系统已过滤，只显示支持聊天对话的模型。</p>
                        <p><strong>随机性 (Temperature)</strong>: 控制回复的随机性。较低的值使回复更加准确和一致，较高的值使回复更加创造性和多样化。</p>
                        <p><strong>最大输出长度 (Max Output Tokens)</strong>: 控制AI回复的最大长度。受所选模型的最大输出token限制约束。</p>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <>
            <div
                className={`fixed inset-0 bg-black transition-opacity duration-300 ${isOpen ? 'bg-opacity-50' : 'bg-opacity-0 pointer-events-none'} z-40`}
                onClick={onClose}
            />
            <div
                className={`fixed top-0 right-0 h-full w-full max-w-md ${themeClasses.bg} shadow-xl z-50 transform transition-transform duration-300 ease-in-out ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}
            >
                <div className="flex flex-col h-full">
                    <div className={`flex justify-between items-center p-6 border-b ${themeClasses.border}`}>
                        <h2 className={`text-xl font-semibold ${themeClasses.text}`}>会话设置</h2>
                        <button onClick={onClose} className={`p-1 rounded-full ${isDarkMode ? 'text-gray-400 hover:bg-gray-700' : 'text-gray-500 hover:bg-gray-100'}`}>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <div className="flex-1 p-6 overflow-y-auto">
                        {renderContent()}
                    </div>
                </div>
            </div>
        </>
    );
};

export default SessionSettingsDrawer; 