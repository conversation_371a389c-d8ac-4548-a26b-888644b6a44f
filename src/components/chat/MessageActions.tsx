import React, { useState, useEffect } from 'react';

interface MessageActionsProps {
    content: string;
    isDarkMode: boolean;
    messageId: string;
    onRegenerate?: (messageId: string) => void;
}

const MessageActions: React.FC<MessageActionsProps> = ({
    content,
    isDarkMode,
    messageId,
    onRegenerate
}) => {
    const [copied, setCopied] = useState(false);
    const [isRegenerating, setIsRegenerating] = useState(false);
    const [isSpeaking, setIsSpeaking] = useState(false);
    const [speechSupported, setSpeechSupported] = useState(false);
    const [currentUtterance, setCurrentUtterance] = useState<SpeechSynthesisUtterance | null>(null);

    // 检查浏览器是否支持语音合成
    useEffect(() => {
        setSpeechSupported('speechSynthesis' in window);
    }, []);

    // 清理文本内容，移除 Markdown 格式
    const cleanTextForSpeech = (text: string): string => {
        return text
            // 移除代码块
            .replace(/```[\s\S]*?```/g, '[代码块]')
            // 移除行内代码
            .replace(/`([^`]+)`/g, '$1')
            // 移除链接，保留文本
            .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
            // 移除粗体和斜体标记
            .replace(/\*\*([^*]+)\*\*/g, '$1')
            .replace(/\*([^*]+)\*/g, '$1')
            .replace(/__([^_]+)__/g, '$1')
            .replace(/_([^_]+)_/g, '$1')
            // 移除标题标记
            .replace(/^#{1,6}\s+/gm, '')
            // 移除列表标记
            .replace(/^[\s]*[-*+]\s+/gm, '')
            .replace(/^\d+\.\s+/gm, '')
            // 移除引用标记
            .replace(/^>\s+/gm, '')
            // 移除多余的空行
            .replace(/\n\s*\n/g, '\n')
            // 移除首尾空白
            .trim();
    };

    // 分段处理长文本
    const splitTextIntoChunks = (text: string, maxLength: number = 200): string[] => {
        const chunks: string[] = [];
        const sentences = text.split(/[。！？.!?]/);
        let currentChunk = '';

        for (const sentence of sentences) {
            if (currentChunk.length + sentence.length > maxLength && currentChunk.length > 0) {
                chunks.push(currentChunk.trim());
                currentChunk = sentence;
            } else {
                currentChunk += (currentChunk.length > 0 ? '。' : '') + sentence;
            }
        }

        if (currentChunk.trim()) {
            chunks.push(currentChunk.trim());
        }

        return chunks.filter(chunk => chunk.length > 0);
    };

    // 语音播报功能
    const handleSpeak = () => {
        if (!speechSupported) {
            console.error('浏览器不支持语音合成功能');
            return;
        }

        // 如果当前消息正在播放，则停止
        if (isSpeaking || window.speechSynthesis.speaking) {
            window.speechSynthesis.cancel();
            setIsSpeaking(false);
            setCurrentUtterance(null);
            return;
        }

        // 确保停止其他可能正在播放的语音
        window.speechSynthesis.cancel();

        // 清理并准备文本
        const textToSpeak = cleanTextForSpeech(content);
        if (!textToSpeak) {
            console.error('没有可播报的内容');
            return;
        }

        // 将长文本分段
        const textChunks = splitTextIntoChunks(textToSpeak);
        let currentChunkIndex = 0;

        const speakChunk = () => {
            if (currentChunkIndex >= textChunks.length) {
                setIsSpeaking(false);
                setCurrentUtterance(null);
                return;
            }

            const utterance = new SpeechSynthesisUtterance(textChunks[currentChunkIndex]);

            // 设置语音参数
            utterance.rate = 1.0; // 语速（0.1 到 10）
            utterance.pitch = 1.0; // 音调（0 到 2）
            utterance.volume = 1.0; // 音量（0 到 1）
            utterance.lang = 'zh-CN'; // 语言

            // 尝试选择中文语音
            const setVoice = () => {
                const voices = window.speechSynthesis.getVoices();
                const chineseVoice = voices.find(voice =>
                    voice.lang.includes('zh') ||
                    voice.lang.includes('CN') ||
                    voice.lang.includes('cmn') ||
                    voice.name.includes('Chinese')
                );
                if (chineseVoice) {
                    utterance.voice = chineseVoice;
                }
            };

            // 如果语音列表已经加载，直接设置
            if (window.speechSynthesis.getVoices().length > 0) {
                setVoice();
            } else {
                // 等待语音列表加载
                window.speechSynthesis.onvoiceschanged = () => {
                    setVoice();
                };
            }

            // 设置事件监听器
            utterance.onstart = () => {
                setIsSpeaking(true);
            };

            utterance.onend = () => {
                currentChunkIndex++;
                speakChunk(); // 播放下一段
            };

            utterance.onerror = (event) => {
                console.error('语音播报错误:', event.error);
                setIsSpeaking(false);
                setCurrentUtterance(null);
            };

            // 保存当前语音实例
            setCurrentUtterance(utterance);

            // 开始播放
            window.speechSynthesis.speak(utterance);
        };

        // 开始播放第一段
        speakChunk();
    };

    // 监听全局语音播报状态变化
    useEffect(() => {
        const handleSpeechStart = () => {
            if (window.speechSynthesis.speaking) {
                setIsSpeaking(true);
            }
        };

        const handleSpeechEnd = () => {
            if (!window.speechSynthesis.speaking) {
                setIsSpeaking(false);
                setCurrentUtterance(null);
            }
        };

        // 定期检查语音播报状态
        const interval = setInterval(() => {
            if (isSpeaking && !window.speechSynthesis.speaking) {
                setIsSpeaking(false);
                setCurrentUtterance(null);
            }
        }, 1000);

        return () => {
            clearInterval(interval);
        };
    }, [isSpeaking]);

    // 组件卸载时清理语音
    useEffect(() => {
        return () => {
            if (currentUtterance) {
                window.speechSynthesis.cancel();
                setIsSpeaking(false);
                setCurrentUtterance(null);
            }
        };
    }, [currentUtterance]);

    // 复制功能
    const handleCopy = async () => {
        try {
            // 检查是否支持现代 Clipboard API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                await navigator.clipboard.writeText(content);
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            } else {
                // 使用传统的复制方法
                const textArea = document.createElement('textarea');
                textArea.value = content;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                if (document.execCommand('copy')) {
                    setCopied(true);
                    setTimeout(() => setCopied(false), 2000);
                } else {
                    console.error('复制失败：浏览器不支持复制操作');
                }

                document.body.removeChild(textArea);
            }
        } catch (err) {
            console.error('复制失败:', err);
            // 最后的备用方案
            try {
                const textArea = document.createElement('textarea');
                textArea.value = content;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                if (document.execCommand('copy')) {
                    setCopied(true);
                    setTimeout(() => setCopied(false), 2000);
                } else {
                    console.error('复制失败：所有复制方法都不可用');
                }

                document.body.removeChild(textArea);
            } catch (fallbackErr) {
                console.error('备用复制方案也失败:', fallbackErr);
            }
        }
    };

    // 重新生成功能
    const handleRegenerate = async () => {
        if (isRegenerating || !onRegenerate) return;

        setIsRegenerating(true);
        try {
            await onRegenerate(messageId);
        } catch (error) {
            console.error('重新生成失败:', error);
        } finally {
            setIsRegenerating(false);
        }
    };

    // 关联信息功能（暂未实现）
    const handleRelatedInfo = () => {
        console.log('关联信息功能暂未实现');
        // TODO: 实现关联信息功能
    };

    // 点赞功能（暂未实现）
    const handleLike = () => {
        console.log('点赞功能暂未实现');
        // TODO: 实现点赞功能
    };

    const buttonClass = `relative p-2 rounded-lg transition-all duration-200 hover:scale-105 group/btn ${isDarkMode
        ? 'hover:bg-gray-700 text-gray-500 hover:text-gray-300'
        : 'hover:bg-gray-100 text-gray-400 hover:text-gray-600'
        }`;

    return (
        <div className="flex items-center space-x-0.5 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out">
            {/* 复制按钮 */}
            <button
                onClick={handleCopy}
                className={`${buttonClass} ${copied ? (isDarkMode ? 'text-green-400' : 'text-green-600') : ''}`}
                title={copied ? '已复制!' : '复制'}
            >
                {copied ? (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                )}
                {/* Tooltip */}
                <div className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs rounded opacity-0 group-hover/btn:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-gray-900 text-white'
                    }`}>
                    {copied ? '已复制!' : '复制'}
                    <div className={`absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent ${isDarkMode ? 'border-t-gray-800' : 'border-t-gray-900'
                        }`}></div>
                </div>
            </button>

            {/* 重新生成按钮 */}
            <button
                onClick={handleRegenerate}
                disabled={isRegenerating || !onRegenerate}
                className={`${buttonClass} ${isRegenerating ? 'opacity-50 cursor-not-allowed' : ''} ${!onRegenerate ? 'opacity-50 cursor-not-allowed' : ''}`}
                title="重新生成"
            >
                <svg
                    className={`w-4 h-4 ${isRegenerating ? 'animate-spin' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {/* Tooltip */}
                <div className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs rounded opacity-0 group-hover/btn:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-gray-900 text-white'
                    }`}>
                    {isRegenerating ? '正在重新生成...' : '重新生成'}
                    <div className={`absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent ${isDarkMode ? 'border-t-gray-800' : 'border-t-gray-900'
                        }`}></div>
                </div>
            </button>

            {/* 语音播报按钮 - 只有在支持语音合成时才显示 */}
            {speechSupported && (
                <button
                    onClick={handleSpeak}
                    className={`${buttonClass} ${isSpeaking ? (isDarkMode ? 'text-blue-400' : 'text-blue-600') : ''}`}
                    title={isSpeaking ? '停止播报' : '语音播报'}
                >
                    {isSpeaking ? (
                        // 停止图标
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10h6v4H9z" />
                        </svg>
                    ) : (
                        // 播放图标
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12a1 1 0 01-1-1V8a1 1 0 011-1h1m-1 4h1m4-4v8a1 1 0 01-1 1H9a1 1 0 01-1-1v-3M9 8h4" />
                        </svg>
                    )}
                    {/* Tooltip */}
                    <div className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs rounded opacity-0 group-hover/btn:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-gray-900 text-white'
                        }`}>
                        {isSpeaking ? '停止播报' : '语音播报'}
                        <div className={`absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent ${isDarkMode ? 'border-t-gray-800' : 'border-t-gray-900'
                            }`}></div>
                    </div>
                </button>
            )}

            {/* 点赞按钮 */}
            <button
                onClick={handleLike}
                className={buttonClass}
                title="点赞"
            >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                </svg>
                {/* Tooltip */}
                <div className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs rounded opacity-0 group-hover/btn:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-gray-900 text-white'
                    }`}>
                    点赞
                    <div className={`absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent ${isDarkMode ? 'border-t-gray-800' : 'border-t-gray-900'
                        }`}></div>
                </div>
            </button>

            {/* 关联信息按钮 */}
            <button
                onClick={handleRelatedInfo}
                className={buttonClass}
                title="关联信息"
            >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                {/* Tooltip */}
                <div className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs rounded opacity-0 group-hover/btn:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-gray-900 text-white'
                    }`}>
                    关联信息
                    <div className={`absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent ${isDarkMode ? 'border-t-gray-800' : 'border-t-gray-900'
                        }`}></div>
                </div>
            </button>

            {/* 分隔线 */}
            <div className={`w-px h-4 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-300'} mx-1`} />

            {/* 更多操作按钮 */}
            <button
                className={buttonClass}
                title="更多操作"
            >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
                {/* Tooltip */}
                <div className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs rounded opacity-0 group-hover/btn:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-gray-900 text-white'
                    }`}>
                    更多操作
                    <div className={`absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent ${isDarkMode ? 'border-t-gray-800' : 'border-t-gray-900'
                        }`}></div>
                </div>
            </button>
        </div>
    );
};

export default MessageActions; 