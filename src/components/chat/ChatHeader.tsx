import React, { useState, useRef, useEffect } from 'react';
import UserMenu from './UserMenu';

interface ChatHeaderProps {
    sidebarVisible: boolean;
    isDarkMode: boolean;
    onToggleSidebar: () => void;
    onToggleTheme: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
    sidebarVisible,
    isDarkMode,
    onToggleSidebar,
    onToggleTheme
}) => {
    const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
    const userMenuRef = useRef<HTMLDivElement>(null);

    // 点击外部关闭用户菜单
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
                setIsUserMenuOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        buttonHover: isDarkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-600',
    };

    return (
        <div className={`${themeClasses.background} px-2 py-2`}>
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    {/* 菜单切换按钮 */}
                    <button
                        onClick={onToggleSidebar}
                        className={`p-2 ${themeClasses.buttonHover} rounded-lg transition-all duration-200 transform hover:scale-110`}
                        title={sidebarVisible ? "隐藏菜单" : "显示菜单"}
                    >
                        <svg
                            className={`w-5 h-5 ${themeClasses.textSecondary} transition-transform duration-300 ${sidebarVisible ? 'rotate-0' : 'rotate-180'
                                }`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
                <div className="flex items-center space-x-3">
                    {/* 主题切换按钮 */}
                    <button
                        onClick={onToggleTheme}
                        className={`p-2 ${themeClasses.buttonHover} rounded-lg transition-all duration-200`}
                        title={isDarkMode ? "切换到亮色主题" : "切换到暗色主题"}
                    >
                        {isDarkMode ? (
                            <svg className={`w-5 h-5 ${themeClasses.textSecondary}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                        ) : (
                            <svg className={`w-5 h-5 ${themeClasses.textSecondary}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                            </svg>
                        )}
                    </button>

                    {/* 用户菜单 */}
                    <UserMenu
                        isOpen={isUserMenuOpen}
                        onToggle={() => setIsUserMenuOpen(!isUserMenuOpen)}
                        isDarkMode={isDarkMode}
                        ref={userMenuRef}
                    />
                </div>
            </div>
        </div>
    );
};

export default ChatHeader; 