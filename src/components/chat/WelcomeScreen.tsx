import React from 'react';

interface Prompt {
    title: string;
    content: string;
}

interface WelcomeScreenProps {
    isDarkMode: boolean;
    onPromptClick: (content: string) => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ isDarkMode, onPromptClick }) => {
    // 预设提示词
    const prompts: Prompt[] = [
        {
            title: "帮我制作一个个人项目的待办事项列表",
            content: "帮我制作一个个人项目的待办事项列表"
        },
        {
            title: "生成任何文章的一段落摘要",
            content: "生成任何文章的一段落摘要"
        },
        {
            title: "为我起草一封推荐技术工作机会的邮件",
            content: "为我起草一封推荐技术工作机会的邮件"
        },
        {
            title: "给我推荐一个今晚的快手简单食谱",
            content: "给我推荐一个今晚的快手简单食谱"
        }
    ];

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-900',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-600',
        inputBg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        cardHover: isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100',
    };

    return (
        <div className="flex-1 flex flex-col items-center justify-center px-8 transition-all duration-700 ease-in-out">
            <div className="max-w-4xl w-full mb-8">
                {/* 左对齐标题 - 优化文案，减小字体 */}
                <h1 className={`text-3xl md:text-4xl font-light ${themeClasses.text} mb-4 leading-tight text-left animate-slideInUp animation-delay-200`}>
                    您好，我是 <span className="text-purple-500 font-medium">锦湖日丽 AI</span>
                </h1>
                <p className={`text-lg md:text-xl ${themeClasses.textSecondary} mb-2 text-left animate-slideInUp animation-delay-300`}>
                    我可以帮您：编写代码、处理文档、回答问题、创意写作、数据分析等
                </p>
            </div>

            {/* 预设提示词卡片 - 简化样式，节省空间 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 max-w-4xl w-full mb-6 animate-slideInUp animation-delay-600">
                {prompts.map((prompt, index) => (
                    <button
                        key={index}
                        onClick={() => onPromptClick(prompt.content)}
                        className={`group relative p-4 ${themeClasses.inputBg} ${themeClasses.cardHover} rounded-2xl text-left shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-[1.02] hover:-translate-y-1`}
                    >
                        {/* 简化的图标 */}
                        <div className="flex items-center space-x-3">
                            <div className={`flex-shrink-0 w-8 h-8 rounded-xl bg-gradient-to-br ${index === 0 ? 'from-purple-500 to-pink-500' :
                                index === 1 ? 'from-blue-500 to-cyan-500' :
                                    index === 2 ? 'from-emerald-500 to-teal-500' :
                                        'from-orange-500 to-red-500'
                                } flex items-center justify-center shadow-sm`}>
                                {index === 0 && (
                                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                    </svg>
                                )}
                                {index === 1 && (
                                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                )}
                                {index === 2 && (
                                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                )}
                                {index === 3 && (
                                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                )}
                            </div>

                            {/* 简化的文字内容 */}
                            <div className="flex-1 min-w-0">
                                <p className={`${themeClasses.text} text-sm font-medium leading-snug group-hover:text-purple-600 transition-colors duration-300 truncate`}>
                                    {prompt.title.length > 20 ? prompt.title.substring(0, 20) + '...' : prompt.title}
                                </p>
                            </div>
                        </div>
                    </button>
                ))}
            </div>

            {/* 新提示词按钮 - 简化样式 */}
            <button className={`flex items-center space-x-2 ${themeClasses.textSecondary} hover:${themeClasses.text} transition-all duration-300 animate-slideInUp animation-delay-800 hover:transform hover:scale-105`}>
                <div className="w-6 h-6 rounded-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                </div>
                <span className="text-sm font-medium">更多示例</span>
            </button>
        </div>
    );
};

export default WelcomeScreen; 