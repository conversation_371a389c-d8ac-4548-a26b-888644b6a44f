import React from 'react';
import { useAgentApps, type AgentApp } from '../../hooks/useOpenAI';

interface AgentAppsProps {
    isDarkMode: boolean;
    onAgentSelect: (agentId: number, agentName: string) => void;
    isWelcomeMode?: boolean;
}

const defaultAvatar = 'https://i.meee.com.tw/WPcPTZB.png';

const AgentApps: React.FC<AgentAppsProps> = ({ isDarkMode, onAgentSelect, isWelcomeMode = false }) => {
    const { data: agentAppsResponse, isLoading, error } = useAgentApps();

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        cardBg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-900',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-600',
        textMuted: isDarkMode ? 'text-gray-500' : 'text-gray-500',
        border: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        hover: isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50',
        loadingBg: isDarkMode ? 'bg-gray-800' : 'bg-gray-100',
    };

    if (isLoading) {
        return (
            <div className={`${isWelcomeMode ? '' : 'px-6 py-4'} ${themeClasses.background}`}>
                <div className="max-w-4xl mx-auto">
                    <h3 className={`text-sm font-medium ${themeClasses.text} mb-3`}>
                        Agent 应用
                    </h3>
                    <div className={`flex space-x-3 overflow-x-auto ${isDarkMode ? 'scrollbar-dark' : 'scrollbar-light'}`}>
                        {[...Array(3)].map((_, index) => (
                            <div
                                key={index}
                                className={`flex-shrink-0 w-48 h-16 rounded-lg animate-pulse shadow-sm`}
                                style={{
                                    background: isDarkMode
                                        ? 'rgba(31, 41, 55, 0.6)'
                                        : 'rgba(249, 250, 251, 0.8)',
                                    border: isDarkMode
                                        ? '1px solid rgba(75, 85, 99, 0.2)'
                                        : '1px solid rgba(229, 231, 235, 0.3)'
                                }}
                            />
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`${isWelcomeMode ? '' : 'px-6 py-4'} ${themeClasses.background}`}>
                <div className="max-w-4xl mx-auto">
                    <h3 className={`text-sm font-medium ${themeClasses.text} mb-3`}>
                        Agent 应用
                    </h3>
                    <div className={`${themeClasses.cardBg} rounded-lg p-4 text-center shadow-sm`}
                        style={{
                            background: isDarkMode
                                ? 'rgba(31, 41, 55, 0.6)'
                                : 'rgba(255, 255, 255, 0.8)',
                            backdropFilter: 'blur(8px)',
                            border: isDarkMode
                                ? '1px solid rgba(75, 85, 99, 0.2)'
                                : '1px solid rgba(229, 231, 235, 0.3)'
                        }}>
                        <svg className={`w-8 h-8 mx-auto mb-2 ${themeClasses.textMuted}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <p className={`text-sm ${themeClasses.textMuted} font-medium`}>
                            加载 Agent 应用失败
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    const agentApps = agentAppsResponse?.data || [];

    if (agentApps.length === 0) {
        return (
            <div className={`${isWelcomeMode ? '' : 'px-6 py-4'} ${themeClasses.background}`}>
                <div className="max-w-4xl mx-auto">
                    <h3 className={`text-sm font-medium ${themeClasses.text} mb-3`}>
                        Agent 应用
                    </h3>
                    <div className={`${themeClasses.cardBg} rounded-lg p-4 text-center shadow-sm`}
                        style={{
                            background: isDarkMode
                                ? 'rgba(31, 41, 55, 0.6)'
                                : 'rgba(255, 255, 255, 0.8)',
                            backdropFilter: 'blur(8px)',
                            border: isDarkMode
                                ? '1px solid rgba(75, 85, 99, 0.2)'
                                : '1px solid rgba(229, 231, 235, 0.3)'
                        }}>
                        <svg className={`w-8 h-8 mx-auto mb-2 ${themeClasses.textMuted}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                        <p className={`text-sm ${themeClasses.textMuted} font-medium`}>
                            暂无可用的 Agent 应用
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className={`${isWelcomeMode ? '' : 'px-6 py-4'} ${themeClasses.background}`}>
            <div className="max-w-4xl mx-auto">
                <h3 className={`text-sm font-medium ${themeClasses.text} mb-3 flex items-center`}>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    Agent 应用
                </h3>

                <div className="relative overflow-hidden">
                    <div className={`flex space-x-3 ${agentApps.length > 3 ? 'animate-scroll-rtl' : ''}`}>
                        {/* 如果应用数量超过3个，复制列表实现循环滚动 */}
                        {agentApps.length > 3 ? [...agentApps, ...agentApps].map((app: AgentApp, index) => (
                            <div
                                key={`${app.id}-${index}`}
                                onClick={() => onAgentSelect(app.id, app.name)}
                                className={`flex-shrink-0 w-48 cursor-pointer group transition-all duration-200 ease-out ${themeClasses.cardBg} rounded-lg p-3 shadow-sm hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-blue-400/15 hover:scale-105 active:scale-95`}
                                style={{
                                    background: isDarkMode
                                        ? 'rgba(31, 41, 55, 0.6)'
                                        : 'rgba(255, 255, 255, 0.8)',
                                    backdropFilter: 'blur(8px)',
                                    border: isDarkMode
                                        ? '1px solid rgba(75, 85, 99, 0.2)'
                                        : '1px solid rgba(229, 231, 235, 0.3)'
                                }}
                            >
                                <div className="flex items-center space-x-3">
                                    <img
                                        src={app.avatar || defaultAvatar}
                                        alt={app.name}
                                        className="w-8 h-8 rounded-lg object-cover bg-gray-200 dark:bg-gray-700 flex-shrink-0 group-hover:scale-110 transition-transform duration-200"
                                    />

                                    <div className="flex-1 min-w-0">
                                        <h4 className={`font-medium text-sm ${themeClasses.text} truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200`}>
                                            {app.name}
                                        </h4>
                                        <p className={`text-xs ${themeClasses.textSecondary} mt-0.5 truncate group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-200`}>
                                            {app.description}
                                        </p>
                                    </div>

                                    <svg className={`w-3 h-3 ${themeClasses.textMuted} group-hover:text-blue-600 dark:group-hover:text-blue-400 opacity-0 group-hover:opacity-100 transition-all duration-200`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                </div>
                            </div>
                        )) : agentApps.map((app: AgentApp) => (
                            <div
                                key={app.id}
                                onClick={() => onAgentSelect(app.id, app.name)}
                                className={`flex-shrink-0 w-48 cursor-pointer group transition-all duration-200 ease-out ${themeClasses.cardBg} rounded-lg p-3 shadow-sm hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-blue-400/15 hover:scale-105 active:scale-95`}
                                style={{
                                    background: isDarkMode
                                        ? 'rgba(31, 41, 55, 0.6)'
                                        : 'rgba(255, 255, 255, 0.8)',
                                    backdropFilter: 'blur(8px)',
                                    border: isDarkMode
                                        ? '1px solid rgba(75, 85, 99, 0.2)'
                                        : '1px solid rgba(229, 231, 235, 0.3)'
                                }}
                            >
                                <div className="flex items-center space-x-3">
                                    <img
                                        src={app.avatar || defaultAvatar}
                                        alt={app.name}
                                        className="w-8 h-8 rounded-lg object-cover bg-gray-200 dark:bg-gray-700 flex-shrink-0 group-hover:scale-110 transition-transform duration-200"
                                    />

                                    <div className="flex-1 min-w-0">
                                        <h4 className={`font-medium text-sm ${themeClasses.text} truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200`}>
                                            {app.name}
                                        </h4>
                                        <p className={`text-xs ${themeClasses.textSecondary} mt-0.5 truncate group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-200`}>
                                            {app.description}
                                        </p>
                                    </div>

                                    <svg className={`w-3 h-3 ${themeClasses.textMuted} group-hover:text-blue-600 dark:group-hover:text-blue-400 opacity-0 group-hover:opacity-100 transition-all duration-200`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AgentApps; 