import React, { forwardRef } from 'react';
import { useNavigate } from 'react-router-dom';
import useAuth from '../../hooks/useAuth';

interface UserMenuProps {
    isOpen: boolean;
    onToggle: () => void;
    isDarkMode: boolean;
}

const UserMenu = forwardRef<HTMLDivElement, UserMenuProps>(({
    isOpen,
    onToggle,
    isDarkMode
}, ref) => {
    const { logout, userInfo } = useAuth();
    const navigate = useNavigate();

    const handleLogout = () => {
        console.log('用户注销登录');
        logout(); // 删除本地token
        onToggle(); // 关闭菜单
        // 强制刷新页面以确保状态更新
        setTimeout(() => {
            window.location.reload();
        }, 100);
    };

    const handleProfileClick = () => {
        console.log('查看个人信息');
        // 这里可以添加个人信息页面的逻辑
    };

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-900',
        buttonHover: isDarkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-600',
        inputBg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        cardBorder: isDarkMode ? 'border-gray-700' : 'border-gray-200',
    };

    return (
        <div className="relative" ref={ref}>
            <button
                onClick={onToggle}
                className={`flex items-center space-x-2 p-2 ${themeClasses.buttonHover} rounded-lg transition-all duration-200 transform hover:scale-105`}
                title="用户菜单"
            >
                <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-cyan-500 rounded-full flex items-center justify-center shadow-lg">
                    {userInfo?.avatar ? (
                        <img
                            src={userInfo.avatar}
                            alt={userInfo.actualName}
                            className="w-8 h-8 rounded-full object-cover"
                        />
                    ) : (
                        <span className="text-white text-sm font-medium">
                            {userInfo?.actualName.charAt(0) || '用'}
                        </span>
                    )}
                </div>
                <svg
                    className={`w-4 h-4 ${themeClasses.textSecondary} transition-transform duration-200 ${isOpen ? 'rotate-180' : 'rotate-0'
                        }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </button>

            {/* 下拉菜单 */}
            {isOpen && (
                <div className={`absolute right-0 mt-2 w-56 ${themeClasses.inputBg} rounded-2xl shadow-xl border ${themeClasses.cardBorder} py-2 z-50 animate-slideInDown`}>
                    {/* 用户信息部分 */}
                    <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                        <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-cyan-500 rounded-full flex items-center justify-center shadow-lg">
                                {userInfo?.avatar ? (
                                    <img
                                        src={userInfo.avatar}
                                        alt={userInfo.actualName}
                                        className="w-10 h-10 rounded-full object-cover"
                                    />
                                ) : (
                                    <span className="text-white font-medium">
                                        {userInfo?.actualName.charAt(0) || '用'}
                                    </span>
                                )}
                            </div>
                            <div>
                                <p className={`${themeClasses.text} font-medium text-sm`}>
                                    {userInfo?.actualName || '用户名称'}
                                </p>
                                <p className={`${themeClasses.textSecondary} text-xs`}>
                                    {userInfo?.email || '<EMAIL>'}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* 菜单选项 */}
                    <div className="py-1">
                        <button
                            onClick={handleProfileClick}
                            className={`w-full flex items-center px-4 py-2 text-sm ${themeClasses.text} ${themeClasses.buttonHover} transition-colors duration-200`}
                        >
                            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            个人信息
                        </button>

                        <button
                            onClick={() => {
                                console.log('设置');
                            }}
                            className={`w-full flex items-center px-4 py-2 text-sm ${themeClasses.text} ${themeClasses.buttonHover} transition-colors duration-200`}
                        >
                            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            设置
                        </button>

                        <div className="border-t border-gray-200 dark:border-gray-700 my-1"></div>

                        <button
                            onClick={handleLogout}
                            className={`w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200`}
                        >
                            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            注销登录
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
});

UserMenu.displayName = 'UserMenu';

export default UserMenu; 