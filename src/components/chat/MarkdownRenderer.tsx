import React, { memo, useMemo, useState, useEffect, useRef, useCallback } from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dark, prism } from 'react-syntax-highlighter/dist/esm/styles/prism';
import remarkGfm from 'remark-gfm';
import mermaid from 'mermaid';
import panzoom from 'panzoom';
import type { Components } from 'react-markdown';

interface MarkdownRendererProps {
    content: string;
    isDarkMode: boolean;
    isStreaming?: boolean;
}

// 复制按钮组件
const CopyButton: React.FC<{
    text: string;
    isDarkMode: boolean;
    label?: string;
}> = ({ text, isDarkMode, label }) => {
    const [copied, setCopied] = useState(false);

    const handleCopy = async () => {
        try {
            await navigator.clipboard.writeText(text);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('复制失败:', err);
        }
    };

    return (
        <button
            onClick={handleCopy}
            className={`flex items-center space-x-1 px-2 py-1 text-xs rounded transition-colors ${isDarkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}
            title={copied ? '已复制!' : `复制${label || ''}`}
        >
            {copied ? (
                <>
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>已复制</span>
                </>
            ) : (
                <>
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    <span>复制</span>
                </>
            )}
        </button>
    );
};

// 代码块工具栏组件
const CodeToolbar: React.FC<{
    language: string;
    code: string;
    isDarkMode: boolean;
}> = ({ language, code, isDarkMode }) => {
    return (
        <div className={`flex items-center justify-between px-4 py-2 ${isDarkMode
            ? 'bg-gray-800'
            : 'bg-gray-50'
            }`}>
            <div className="flex items-center space-x-2">
                <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                    {language || 'text'}
                </span>
            </div>
            <CopyButton text={code} isDarkMode={isDarkMode} label="代码" />
        </div>
    );
};

// 表格工具栏组件
const TableToolbar: React.FC<{
    children: React.ReactNode;
    isDarkMode: boolean;
}> = ({ children, isDarkMode }) => {
    // 从子元素中提取纯文本内容用于复制
    const extractTableText = (element: React.ReactNode): string => {
        if (!element) return '';

        // 简单的文本提取逻辑
        const extractText = (node: any): string => {
            if (typeof node === 'string') return node;
            if (typeof node === 'number') return String(node);
            if (React.isValidElement(node)) {
                const props = node.props as any;
                if (props?.children) {
                    if (Array.isArray(props.children)) {
                        return props.children.map(extractText).join('');
                    }
                    return extractText(props.children);
                }
            }
            if (Array.isArray(node)) {
                return node.map(extractText).join('');
            }
            return '';
        };

        return extractText(element);
    };

    const tableText = extractTableText(children);

    return (
        <div className={`flex items-center justify-between px-4 py-2 ${isDarkMode
            ? 'bg-gray-800'
            : 'bg-gray-50'
            }`}>
            <div className="flex items-center space-x-2">
                <svg className={`w-4 h-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                    表格
                </span>
            </div>
            <CopyButton text={tableText} isDarkMode={isDarkMode} label="表格数据" />
        </div>
    );
};

// Mermaid图表组件 - 使用专业panzoom库
const MermaidChart: React.FC<{
    chart: string;
    isDarkMode: boolean;
}> = ({ chart, isDarkMode }) => {
    const [svgContent, setSvgContent] = useState<string>('');
    const [error, setError] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isFullscreen, setIsFullscreen] = useState(false);

    const containerRef = useRef<HTMLDivElement>(null);
    const fullscreenRef = useRef<HTMLDivElement>(null);
    const panzoomInstance = useRef<any>(null);

    useEffect(() => {
        let isMounted = true;

        const renderChart = async () => {
            try {
                console.log('开始渲染Mermaid图表，内容:', chart);
                setIsLoading(true);
                setError(null);
                setSvgContent('');

                // 简化的mermaid配置
                mermaid.initialize({
                    startOnLoad: false,
                    theme: isDarkMode ? 'dark' : 'default',
                    securityLevel: 'loose'
                });

                // 生成唯一ID
                const id = `chart-${Date.now()}`;
                console.log('使用ID:', id);

                // 使用Promise包装确保错误处理
                const result = await new Promise((resolve, reject) => {
                    // 设置超时避免无限等待
                    const timeout = setTimeout(() => {
                        reject(new Error('渲染超时'));
                    }, 10000);

                    mermaid.render(id, chart)
                        .then((result) => {
                            clearTimeout(timeout);
                            resolve(result);
                        })
                        .catch((err) => {
                            clearTimeout(timeout);
                            reject(err);
                        });
                });

                if (isMounted && result && (result as any).svg) {
                    const svg = (result as any).svg;
                    console.log('渲染成功，SVG长度:', svg.length);
                    setSvgContent(svg);
                    setIsLoading(false);
                } else {
                    throw new Error('渲染结果无效');
                }

            } catch (err) {
                console.error('Mermaid渲染失败:', err);
                if (isMounted) {
                    setError(err instanceof Error ? err.message : '渲染失败');
                    setIsLoading(false);
                }
            }
        };

        // 确保在下一个事件循环中执行
        const timer = setTimeout(renderChart, 50);

        return () => {
            isMounted = false;
            clearTimeout(timer);
        };
    }, [chart, isDarkMode]);

    // 初始化panzoom
    useEffect(() => {
        if (svgContent && containerRef.current) {
            const svgElement = containerRef.current.querySelector('svg');
            if (svgElement) {
                // 清理旧的实例
                if (panzoomInstance.current) {
                    panzoomInstance.current.dispose();
                }

                // 创建新的panzoom实例
                panzoomInstance.current = panzoom(svgElement, {
                    maxZoom: 3,
                    minZoom: 0.5,
                    zoomSpeed: 0.2,
                    smoothScroll: false,
                    beforeWheel: function (e) {
                        // 只在按住Ctrl/Cmd时允许滚轮缩放
                        return !e.ctrlKey && !e.metaKey;
                    }
                });


            }
        }

        return () => {
            if (panzoomInstance.current) {
                panzoomInstance.current.dispose();
                panzoomInstance.current = null;
            }
        };
    }, [svgContent]);



    const handleFullscreen = useCallback(async () => {
        if (!fullscreenRef.current) return;

        try {
            if (!isFullscreen) {
                await fullscreenRef.current.requestFullscreen();
            } else {
                await document.exitFullscreen();
            }
        } catch (error) {
            console.error('全屏操作失败:', error);
        }
    }, [isFullscreen]);

    // 全屏功能
    useEffect(() => {
        const handleFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement);
        };

        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && isFullscreen) {
                handleFullscreen();
            }
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('keydown', handleKeyDown);

        return () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [isFullscreen, handleFullscreen]);

    if (error) {
        return (
            <div className={`p-4 rounded-lg border-2 border-dashed ${isDarkMode ? 'border-red-500/30 bg-red-500/10 text-red-300' : 'border-red-400/50 bg-red-50 text-red-600'
                }`}>
                <div className="flex items-center space-x-2 mb-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-medium">Mermaid图表渲染失败</span>
                </div>
                <p className="text-sm mb-3">{error}</p>
                <details className="text-xs">
                    <summary className="cursor-pointer hover:underline">查看原始代码</summary>
                    <pre className="mt-2 p-2 rounded bg-black/20 overflow-x-auto">
                        <code>{chart}</code>
                    </pre>
                </details>
            </div>
        );
    }

    return (
        <div
            ref={fullscreenRef}
            className={`relative rounded-lg overflow-hidden my-4 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
                } ${isFullscreen ? 'fixed inset-0 z-50 m-0 rounded-none' : ''}`}
        >
            <div className={`flex items-center justify-between px-4 py-2 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'
                }`}>
                <div className="flex items-center space-x-2">
                    <svg className={`w-4 h-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span className={`text-xs font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                        Mermaid图表
                    </span>
                    <span className={`text-xs ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`}>
                        • Ctrl+滚轮缩放 • 拖拽移动
                    </span>
                </div>
                <div className="flex items-center space-x-1">
                    <button
                        onClick={handleFullscreen}
                        className={`p-1 rounded transition-colors ${isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-200 text-gray-600'
                            }`}
                        title={isFullscreen ? "退出全屏" : "全屏查看"}
                    >
                        {isFullscreen ? (
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0-4.5l5.5 5.5" />
                            </svg>
                        ) : (
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                            </svg>
                        )}
                    </button>
                    <CopyButton text={chart} isDarkMode={isDarkMode} label="图表代码" />
                </div>
            </div>

            {isLoading ? (
                <div className={`flex items-center justify-center p-8 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                    <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                        <span className="text-sm">正在渲染图表...</span>
                    </div>
                </div>
            ) : (
                <div
                    ref={containerRef}
                    className={`p-4 overflow-hidden ${isFullscreen ? '' : 'border rounded'}`}
                    style={{
                        maxWidth: '100%',
                        maxHeight: isFullscreen ? 'calc(100vh - 60px)' : '600px',
                        minHeight: isFullscreen ? 'calc(100vh - 60px)' : '200px',
                        position: 'relative'
                    }}
                >
                    <div
                        className="mermaid-container"
                        style={{
                            minWidth: 'fit-content'
                        }}
                        dangerouslySetInnerHTML={{ __html: svgContent }}
                    />
                </div>
            )}
        </div>
    );
};

// 优化的Markdown块组件
const MemoizedMarkdownBlock = memo(
    ({ content, isDarkMode }: { content: string; isDarkMode: boolean }) => {
        const components: Components = {
            // 代码块渲染 - 保留语法高亮并添加工具栏，新增Mermaid支持
            code({ className, children, ...props }) {
                const match = /language-(\w+)/.exec(className || '');
                const language = match ? match[1] : '';
                const code = String(children).replace(/\n$/, '');

                // 检查是否是代码块（通过className判断，代码块通常有language-前缀）
                const isCodeBlock = className && className.startsWith('language-');

                if (isCodeBlock) {
                    // Mermaid图表渲染
                    if (language === 'mermaid') {
                        return <MermaidChart chart={code} isDarkMode={isDarkMode} />;
                    }

                    // 普通代码块渲染
                    return (
                        <div className={`relative rounded-md overflow-hidden my-4 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
                            }`}>
                            <CodeToolbar language={language || 'text'} code={code} isDarkMode={isDarkMode} />
                            <SyntaxHighlighter
                                style={isDarkMode ? dark : prism}
                                language={language || 'text'}
                                PreTag="div"
                                className="!mt-0 !mb-0"
                                customStyle={{
                                    margin: 0,
                                    borderRadius: 0,
                                    background: 'transparent',
                                    fontSize: '14px',
                                    fontWeight: '1000'
                                }}
                            >
                                {code}
                            </SyntaxHighlighter>
                        </div>
                    );
                } else {
                    // 内联代码
                    return (
                        <code
                            className={`px-1 py-0.5 rounded text-sm ${isDarkMode
                                ? 'bg-gray-700 text-gray-200'
                                : 'bg-gray-100 text-gray-800'
                                } ${className || ''}`}
                            {...props}
                        >
                            {children}
                        </code>
                    );
                }
            },
            // Pre 标签处理 - 防止默认样式干扰
            pre({ children, ...props }) {
                return <>{children}</>;
            },
            // 表格渲染 - 添加工具栏
            table({ children, ...props }) {
                return (
                    <div className={`relative rounded-md overflow-hidden my-4 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
                        }`}>
                        <TableToolbar children={children} isDarkMode={isDarkMode} />
                        <div className="overflow-x-auto">
                            <table
                                className="min-w-full !mt-0 !mb-0"
                                {...props}
                            >
                                {children}
                            </table>
                        </div>
                    </div>
                );
            },
        };

        return (
            <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={components}
            >
                {content}
            </ReactMarkdown>
        );
    },
    (prevProps, nextProps) => {
        return (
            prevProps.content === nextProps.content &&
            prevProps.isDarkMode === nextProps.isDarkMode
        );
    }
);

MemoizedMarkdownBlock.displayName = 'MemoizedMarkdownBlock';

// 内联的 Thinking 组件
const Thinking: React.FC<{ content: string; isDarkMode: boolean; isStreaming: boolean }> = ({ content, isDarkMode, isStreaming }) => {
    const [isExpanded, setIsExpanded] = useState(isStreaming);
    const prevIsStreaming = useRef(isStreaming);

    useEffect(() => {
        // 当流式传输开始时，强制展开
        if (isStreaming) {
            setIsExpanded(true);
        }
        // 当流式传输刚刚结束时（从 true 变为 false），自动折叠
        else if (prevIsStreaming.current && !isStreaming) {
            setIsExpanded(false);
        }
        // 更新上一状态
        prevIsStreaming.current = isStreaming;
    }, [isStreaming]);

    const themeClasses = {
        background: isDarkMode ? 'bg-black/20' : 'bg-gray-100/80',
        border: isDarkMode ? 'border-blue-500/30' : 'border-blue-400/50',
        text: isDarkMode ? 'text-gray-400' : 'text-gray-500',
        headerText: isDarkMode ? 'text-gray-300' : 'text-gray-600',
        hoverBg: isDarkMode ? 'hover:bg-black/30' : 'hover:bg-gray-200/80',
    };

    // 折叠时只显示一个简约的条
    if (!isExpanded) {
        // 如果没有思考内容，且不在流式传输中，则不渲染任何东西
        if (!content && !isStreaming) return null;

        return (
            <button
                onClick={() => setIsExpanded(true)}
                className={`w-full text-left px-4 py-2 my-2 rounded-lg flex items-center justify-between text-sm transition-colors duration-200 ${themeClasses.background} ${themeClasses.hoverBg}`}
            >
                <div className="flex items-center space-x-2">
                    <svg className={`w-4 h-4 transition-transform duration-200 text-gray-400`} fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" /></svg>
                    <span className={`font-medium ${themeClasses.headerText}`}>
                        模型思考过程...
                    </span>
                </div>
                <span className={`text-xs ${themeClasses.text}`}>点击展开</span>
            </button>
        )
    }

    // 展开后的样式
    return (
        <div className={`my-4 animate-fadeIn`}>
            <button
                onClick={() => setIsExpanded(false)}
                className={`w-full px-3 py-2 flex items-center justify-between rounded-t-lg transition-colors duration-200 ${isDarkMode ? 'bg-gray-800/60 hover:bg-gray-700/60' : 'bg-gray-100 hover:bg-gray-200'}`}
            >
                <div className="flex items-center space-x-1.5">
                    <svg className={`w-3.5 h-3.5 transition-transform duration-200 text-gray-400`} fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" /></svg>
                    <span className={`font-medium text-gray-300 text-xs`}>
                        模型思考过程
                    </span>
                </div>
                <span className={`text-xs text-gray-500`}>点击折叠</span>
            </button>

            <div className={`p-4 border-l-2 ${themeClasses.border}`}>
                <div className={`prose prose-sm ${isDarkMode ? 'prose-invert' : ''} max-w-none`}>
                    <MemoizedMarkdownBlock
                        content={content}
                        isDarkMode={isDarkMode}
                    />
                    {isStreaming && (
                        <span className="inline-block w-2 h-4 bg-current animate-pulse ml-1" />
                    )}
                </div>
            </div>
        </div>
    );
};

// 流式Markdown渲染器
export const MarkdownRenderer: React.FC<MarkdownRendererProps> = memo(
    ({ content, isDarkMode, isStreaming = false }) => {
        const { thinkingContent, responseContent, isThinkingStreaming } = useMemo(() => {
            const thinkRegex = /<think>([\s\S]*?)<\/think>/;
            const thinkMatch = content.match(thinkRegex);

            if (thinkMatch) {
                const thinking = thinkMatch[1] || '';
                const response = content.replace(thinkRegex, '');
                return { thinkingContent: thinking, responseContent: response, isThinkingStreaming: false };
            }

            // 处理流式思考
            const thinkStartRegex = /<think>([\s\S]*)/;
            const thinkStartMatch = content.match(thinkStartRegex);

            if (thinkStartMatch) {
                const thinking = thinkStartMatch[1] || '';
                // 在流式思考中，即使思考内容为空也要渲染组件以显示"正在思考"
                return { thinkingContent: thinking, responseContent: '', isThinkingStreaming: true };
            }

            return { thinkingContent: null, responseContent: content, isThinkingStreaming: false };
        }, [content]);


        return (
            <div className={`prose ${isDarkMode ? 'prose-invert' : ''} max-w-none`}>
                {thinkingContent !== null && (
                    <Thinking
                        content={thinkingContent}
                        isDarkMode={isDarkMode}
                        isStreaming={isStreaming && isThinkingStreaming}
                    />
                )}

                <MemoizedMarkdownBlock
                    content={responseContent}
                    isDarkMode={isDarkMode}
                />

                {isStreaming && !isThinkingStreaming && responseContent && (
                    <span className="inline-block w-2 h-5 bg-current animate-pulse ml-1" />
                )}
            </div>
        );
    }
);

MarkdownRenderer.displayName = 'MarkdownRenderer';

export default MarkdownRenderer;