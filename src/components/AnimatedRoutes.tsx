import React, { lazy, Suspense } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { AnimatePresence, motion, Transition } from 'framer-motion';
import ChatInterface from '../pages/ChatInterface';
import DingTalkCallback from './DingTalkCallback';
import Loading from './Loading';

// 懒加载组件以提高性能
const AIApps = lazy(() => import('../pages/AIApps'));
const KnowledgeBase = lazy(() => import('../pages/KnowledgeBase'));
const VisualModelTop = lazy(() => import('../pages/VisualModelTop'));
const Library = lazy(() => import('../pages/Library'));
const Images = lazy(() => import('../pages/Images'));

interface AnimatedRoutesProps {
    sidebarVisible: boolean;
    isDarkMode: boolean;
    onToggleSidebar: () => void;
    onToggleTheme: () => void;
}

const getRouteKey = (pathname: string) => {
    if (pathname.startsWith('/chat') || pathname === '/') {
        return '/chat';
    }
    if (pathname.startsWith('/visual-model')) {
        return '/visual-model';
    }
    if (pathname.startsWith('/library')) {
        return '/library';
    }
    if (pathname.startsWith('/images')) {
        return '/images';
    }
    return pathname.split('/')[1] || '/';
};

const AnimatedRoutes: React.FC<AnimatedRoutesProps> = React.memo(({
    sidebarVisible,
    isDarkMode,
    onToggleSidebar,
    onToggleTheme
}) => {
    const location = useLocation();
    const backgroundClass = isDarkMode ? 'bg-gray-900' : 'bg-white';
    const routeKey = getRouteKey(location.pathname);

    const pageVariants = {
        initial: {
            opacity: 0,
            scale: 0.95
        },
        in: {
            opacity: 1,
            scale: 1
        },
        out: {
            opacity: 0,
            scale: 0.95
        }
    };

    const pageTransition: Transition = {
        type: 'tween',
        ease: 'easeOut',
        duration: 0.3
    };

    return (
        <div className="relative w-full h-full overflow-hidden">
            <AnimatePresence mode="wait" initial={false}>
                <motion.div
                    key={routeKey}
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                    className={`absolute w-full h-full ${backgroundClass}`}
                >
                    <Suspense fallback={<Loading />}>
                        <Routes location={location}>
                            <Route
                                path="/"
                                element={
                                    <ChatInterface
                                        sidebarVisible={sidebarVisible}
                                        isDarkMode={isDarkMode}
                                        onToggleSidebar={onToggleSidebar}
                                        onToggleTheme={onToggleTheme}
                                    />
                                }
                            />
                            <Route
                                path="/chat/:sessionId"
                                element={
                                    <ChatInterface
                                        sidebarVisible={sidebarVisible}
                                        isDarkMode={isDarkMode}
                                        onToggleSidebar={onToggleSidebar}
                                        onToggleTheme={onToggleTheme}
                                    />
                                }
                            />
                            <Route
                                path="/apps"
                                element={
                                    <AIApps
                                        isDarkMode={isDarkMode}
                                    // onToggleTheme={onToggleTheme}
                                    />
                                }
                            />
                            <Route
                                path="/knowledge"
                                element={
                                    <KnowledgeBase
                                        isDarkMode={isDarkMode}
                                    // onToggleTheme={onToggleTheme}
                                    />
                                }
                            />
                            <Route
                                path="/visual-model/top"
                                element={
                                    <VisualModelTop
                                        isDarkMode={isDarkMode}
                                    // onToggleTheme={onToggleTheme}
                                    />
                                }
                            />
                            <Route
                                path="/library"
                                element={
                                    <Library
                                        isDarkMode={isDarkMode}
                                    // onToggleTheme={() => { }}
                                    />
                                }
                            />
                            <Route
                                path="/images"
                                element={
                                    <Images
                                        isDarkMode={isDarkMode}
                                    />
                                }
                            />
                            <Route
                                path="/dingtalk-callback"
                                element={
                                    <DingTalkCallback
                                        isDarkMode={isDarkMode}
                                    />
                                }
                            />
                        </Routes>
                    </Suspense>
                </motion.div>
            </AnimatePresence>
        </div>
    );
});

AnimatedRoutes.displayName = 'AnimatedRoutes';

export default AnimatedRoutes; 