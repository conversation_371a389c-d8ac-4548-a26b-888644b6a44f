import React, { useEffect, useState } from 'react';
import useAuth from '../hooks/useAuth';
import DingTalkLoginModal from './DingTalkLogin';

interface AuthGuardProps {
    children: React.ReactNode;
    isDarkMode?: boolean;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children, isDarkMode = false }) => {
    const { isAuthenticated } = useAuth();
    const [showLoginModal, setShowLoginModal] = useState(false);

    useEffect(() => {
        // 检查是否已认证，如果没有认证则显示登录模态框
        console.log('AuthGuard: 认证状态变化', isAuthenticated);
        if (!isAuthenticated) {
            setShowLoginModal(true);
        } else {
            setShowLoginModal(false);
        }
    }, [isAuthenticated]);

    const handleCloseModal = () => {
        // 强制登录模式下，不允许关闭模态框
        // setShowLoginModal(false);
        console.log('强制登录模式，不允许关闭身份验证模态框');
    };

    return (
        <>
            {children}
            <DingTalkLoginModal
                isOpen={showLoginModal}
                onClose={handleCloseModal}
                isDarkMode={isDarkMode}
                forceLogin={true}
            />
        </>
    );
};

export default AuthGuard; 