import React from 'react';
import { DINGTALK_CONFIG } from '../utils/constants';

interface DingTalkLoginModalProps {
    isOpen: boolean;
    onClose: () => void;
    isDarkMode?: boolean;
    forceLogin?: boolean; // 是否强制登录，不允许关闭
}

const DingTalkLoginModal: React.FC<DingTalkLoginModalProps> = ({
    isOpen,
    onClose,
    isDarkMode = false,
    forceLogin = false
}) => {
    const handleDingTalkLogin = () => {
        try {
            // 验证配置
            if (!DINGTALK_CONFIG.CLIENT_ID) {
                console.error('钉钉 CLIENT_ID 未配置');
                alert('钉钉登录配置错误：CLIENT_ID 未设置');
                return;
            }

            if (!DINGTALK_CONFIG.CALLBACK_URL) {
                console.error('钉钉 CALLBACK_URL 未配置');
                alert('钉钉登录配置错误：CALLBACK_URL 未设置');
                return;
            }

            // 构建钉钉登录授权URL
            const params = new URLSearchParams({
                redirect_uri: DINGTALK_CONFIG.CALLBACK_URL,
                response_type: 'code',
                client_id: DINGTALK_CONFIG.CLIENT_ID,
                scope: 'openid',
                state: 'login_state_' + Date.now(),
                prompt: 'consent'
            });

            // 跳转到钉钉登录授权页面
            const authUrl = `https://login.dingtalk.com/oauth2/auth?${params.toString()}`;

            console.log('钉钉登录配置信息:');
            console.log('CLIENT_ID:', DINGTALK_CONFIG.CLIENT_ID);
            console.log('CALLBACK_URL:', DINGTALK_CONFIG.CALLBACK_URL);
            console.log('URLSearchParams自动编码后的参数:', params.toString());
            console.log('完整授权URL:', authUrl);

            window.location.href = authUrl;
        } catch (error) {
            console.error('钉钉登录发生错误:', error);
            alert('钉钉登录失败，请检查控制台错误信息');
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            {/* 背景模糊遮罩 */}
            <div
                className="absolute inset-0 bg-black/50 backdrop-blur-sm"
                onClick={forceLogin ? undefined : onClose}
            />

            {/* Modal内容 */}
            <div className={`relative w-full max-w-sm mx-auto ${isDarkMode
                ? 'bg-gray-800 border-gray-700'
                : 'bg-white border-gray-200'
                } rounded-2xl shadow-2xl border backdrop-blur-lg`}>

                {/* 关闭按钮 - 强制登录时隐藏 */}
                {!forceLogin && (
                    <button
                        onClick={onClose}
                        className={`absolute top-4 right-4 p-2 rounded-full transition-colors ${isDarkMode
                            ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                            : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                            }`}
                    >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                )}

                <div className="p-8">
                    {/* 头部图标和标题 */}
                    <div className="text-center mb-8">
                        <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${isDarkMode ? 'bg-blue-600' : 'bg-blue-500'
                            }`}>
                            <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z" />
                            </svg>
                        </div>
                        <h2 className={`text-2xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'
                            }`}>
                            身份验证
                        </h2>
                        <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'
                            }`}>
                            请使用钉钉账号登录以继续使用
                        </p>
                    </div>

                    {/* 登录按钮 */}
                    <button
                        onClick={handleDingTalkLogin}
                        className={`w-full flex items-center justify-center px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 active:scale-95 ${isDarkMode
                            ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg shadow-blue-600/25'
                            : 'bg-blue-500 hover:bg-blue-600 text-white shadow-lg shadow-blue-500/25'
                            }`}
                    >
                        <svg
                            className="w-5 h-5 mr-3"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                        >
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 4L13.5 7.5C13.1 8.4 12.2 9 11.2 9H8.8C7.8 9 6.9 8.4 6.5 7.5L5 4L3 7V9C3 9.6 3.4 10 4 10S5 9.6 5 9V8L6 7V10.5C6 11.3 6.7 12 7.5 12S9 11.3 9 10.5V12H15V10.5C15 11.3 15.7 12 16.5 12S18 11.3 18 10.5V7L19 8V9C19 9.6 19.4 10 20 10S21 9.6 21 9Z" />
                        </svg>
                        通过钉钉登录
                    </button>

                    {/* 底部提示 */}
                    <p className={`text-center text-xs mt-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                        登录即表示您同意我们的服务条款
                    </p>
                </div>
            </div>
        </div>
    );
};

export default DingTalkLoginModal; 