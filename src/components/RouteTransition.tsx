import React from 'react';
import { TransitionGroup, CSSTransition } from 'react-transition-group';
import { useLocation } from 'react-router-dom';

interface RouteTransitionProps {
    children: React.ReactNode;
}

const RouteTransition: React.FC<RouteTransitionProps> = ({ children }) => {
    const location = useLocation();

    return (
        <TransitionGroup className="route-transition-group">
            <CSSTransition
                key={location.pathname}
                classNames="route"
                timeout={300}
                unmountOnExit
            >
                <div className="route-wrapper">
                    {children}
                </div>
            </CSSTransition>
        </TransitionGroup>
    );
};

export default RouteTransition; 