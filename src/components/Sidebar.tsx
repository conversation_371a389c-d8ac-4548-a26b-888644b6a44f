import React, { useState } from 'react';
import useAuth from '../hooks/useAuth';
import useSession from '../hooks/useSession';
import { SessionData, chatSessionApi } from '../utils/api';
import Modal from './Modal';
import { Sun, Moon, LogOut, Settings } from 'lucide-react';

interface SidebarProps {
    visible: boolean;
    isDarkMode: boolean;
    onToggleTheme: () => void;
    currentSessionId: string | null;
    onSelectConversation: (id: string) => void;
    onNewConversation: () => void;
    onNavigateToApps?: () => void;
    onNavigateToKnowledge?: () => void;
    onNavigateToVisualTop?: () => void;
    onNavigateToLibrary?: () => void;
    onNavigateToImages?: () => void;
    pathname?: string;
}

const Sidebar: React.FC<SidebarProps> = ({
    visible,
    isDarkMode,
    onToggleTheme,
    currentSessionId,
    onSelectConversation,
    onNewConversation,
    onNavigateToApps,
    onNavigateToKnowledge,
    onNavigateToVisualTop,
    onNavigateToLibrary,
    onNavigateToImages,
    pathname
}) => {
    const { logout, userInfo, token } = useAuth();
    const { sessions, isLoadingSessions, sessionsError, refreshSessions, deleteSession } = useSession();
    const [sessionToDelete, setSessionToDelete] = useState<SessionData | null>(null);
    const [isNewChatModalVisible, setIsNewChatModalVisible] = useState(false);
    const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
    const [editingTitle, setEditingTitle] = useState<string>('');
    const [isUpdatingTitle, setIsUpdatingTitle] = useState(false);

    const handleLogout = () => {
        logout();
        // 刷新页面，确保AuthGuard重新检查认证状态
        setTimeout(() => {
            window.location.reload();
        }, 100);
    };

    const handleNewConversationClick = () => {
        setIsNewChatModalVisible(true);
    };

    const handleConfirmNewConversation = () => {
        onNewConversation();
        setIsNewChatModalVisible(false);
    };

    const handleDeleteClick = (e: React.MouseEvent, session: SessionData) => {
        e.stopPropagation();
        setSessionToDelete(session);
    };

    const handleConfirmDelete = async () => {
        if (sessionToDelete) {
            try {
                await deleteSession(sessionToDelete.id);
            } catch (error) {
                console.error("Failed to delete session", error);
                // Optionally, show an error message to the user
            } finally {
                setSessionToDelete(null);
            }
        }
    };

    const handleEditClick = (e: React.MouseEvent, session: SessionData) => {
        e.stopPropagation();
        setEditingSessionId(session.id);
        setEditingTitle(session.sessionTitle);
    };

    const handleSaveEdit = async () => {
        if (!editingSessionId || !token || isUpdatingTitle) return;

        setIsUpdatingTitle(true);
        try {
            await chatSessionApi.updateSession(editingSessionId, {
                title: editingTitle.trim()
            }, token);

            // 更新成功后刷新会话列表
            refreshSessions();
            setEditingSessionId(null);
            setEditingTitle('');
        } catch (error) {
            console.error("Failed to update session title", error);
            // 可以在这里添加错误提示
        } finally {
            setIsUpdatingTitle(false);
        }
    };

    const handleCancelEdit = () => {
        setEditingSessionId(null);
        setEditingTitle('');
    };

    const handleTitleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            handleSaveEdit();
        } else if (e.key === 'Escape') {
            handleCancelEdit();
        }
    };

    // 安全地解析 metadata
    const parseMetadata = (metadataString: string) => {
        try {
            return JSON.parse(metadataString);
        } catch (e) {
            return {};
        }
    };

    const sortedSessions = React.useMemo(() => {
        if (!sessions) return [];
        return [...sessions].sort((a, b) => {
            // "active" 状态的会话排在前面
            if (a.status === 'active' && b.status !== 'active') return -1;
            if (a.status !== 'active' && b.status === 'active') return 1;

            // 如果状态相同，则按更新时间倒序排列
            return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        });
    }, [sessions]);

    const formatTime = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMins < 60) {
            return `${diffMins}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else {
            return `${diffDays}天前`;
        }
    };

    const handleRefreshSessions = () => {
        refreshSessions();
    };

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-900',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-600',
        textTertiary: isDarkMode ? 'text-gray-500' : 'text-gray-500',
        border: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        hover: isDarkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100',
        selected: isDarkMode ? 'bg-gray-800' : 'bg-gray-200'
    };

    return (
        <div className={`h-full ${themeClasses.background} ${themeClasses.text} flex flex-col`}>
            {/* 顶部区域 */}
            <div className="p-4">
                {/* 品牌标识 */}
                <div className="flex items-center space-x-3 mb-4">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M12 6V3m0 18v-3m6-6h3m-18 0h3M9 6h6v12H9z" />
                        </svg>
                    </div>
                    <div>
                        <h2 className={`text-base font-bold ${themeClasses.text}`}></h2>
                    </div>
                </div>

                {/* 操作按钮 */}
                <div className="space-y-2">
                    {/* 新建对话按钮 */}
                    <button
                        onClick={handleNewConversationClick}
                        className={`w-full ${themeClasses.hover} ${themeClasses.text} py-2.5 px-3 rounded-lg font-medium text-sm transition-colors duration-200 flex items-center space-x-3`}
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        <span>新建对话</span>
                    </button>

                    {/* 应用中心按钮 */}
                    <button
                        onClick={onNavigateToApps}
                        className={`w-full ${pathname === '/apps' ? themeClasses.selected : themeClasses.hover} ${themeClasses.text} py-2.5 px-3 rounded-lg font-medium text-sm transition-colors duration-200 flex items-center space-x-3`}
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        <span>应用中心</span>
                    </button>

                    {/* 知识库按钮 */}
                    <button
                        onClick={onNavigateToKnowledge}
                        className={`w-full ${pathname === '/knowledge' ? themeClasses.selected : themeClasses.hover} ${themeClasses.text} py-2.5 px-3 rounded-lg font-medium text-sm transition-colors duration-200 flex items-center space-x-3`}
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        <span>知识库</span>
                    </button>
                </div>
            </div>

            {/* 视觉分类区域 - 固定不滚动 */}
            <div className="px-4 pb-2">
                <div className="mb-6">
                    <h3 className={`text-xs font-medium ${themeClasses.textSecondary} mb-3`}>创作中心</h3>
                    <div className="space-y-1">
                        {/* 示例展示 */}
                        <button
                            onClick={onNavigateToImages}
                            className={`w-full text-left ${pathname === '/images' ? themeClasses.selected : themeClasses.hover} ${themeClasses.text} py-2 px-3 rounded-lg font-medium text-sm transition-colors duration-200 flex items-center space-x-3`}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <span>示例展示</span>
                        </button>
                        {/* Videos - 暂时隐藏 */}
                        {/* <button
                            className={`w-full text-left ${themeClasses.hover} ${themeClasses.text} py-2 px-3 rounded-lg font-medium text-sm transition-colors duration-200 flex items-center space-x-3`}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                            <span>Videos</span>
                        </button> */}
                        {/* Top - 暂时隐藏 */}
                        {/* <button
                            onClick={onNavigateToVisualTop}
                            className={`w-full text-left ${pathname === '/visual-model/top' ? themeClasses.selected : themeClasses.hover} ${themeClasses.text} py-2 px-3 rounded-lg font-medium text-sm transition-colors duration-200 flex items-center space-x-3`}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.783-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                            </svg>
                            <span>Top</span>
                        </button> */}
                        {/* Like - 暂时隐藏 */}
                        {/* <button
                            className={`w-full text-left ${themeClasses.hover} ${themeClasses.text} py-2 px-3 rounded-lg font-medium text-sm transition-colors duration-200 flex items-center space-x-3`}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                            <span>Like</span>
                        </button> */}
                        {/* AI创作 */}
                        <button
                            onClick={onNavigateToLibrary}
                            className={`w-full text-left ${pathname === '/library' ? themeClasses.selected : themeClasses.hover} ${themeClasses.text} py-2 px-3 rounded-lg font-medium text-sm transition-colors duration-200 flex items-center space-x-3`}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                            </svg>
                            <span>AI图像创作</span>
                        </button>
                    </div>
                </div>
            </div>

            {/* 对话历史区域 - 可滚动 */}
            <div className="flex-1 overflow-y-auto">
                <div className="px-4">
                    <div className="flex items-center justify-between mb-3">
                        <h3 className={`text-xs font-medium ${themeClasses.textSecondary}`}>最近对话</h3>
                        <button
                            onClick={handleRefreshSessions}
                            className={`text-xs ${themeClasses.textSecondary} hover:${themeClasses.text} transition-colors duration-200 p-1 rounded`}
                            title="刷新会话列表"
                        >
                            <svg className={`w-3 h-3 ${isLoadingSessions ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </button>
                    </div>

                    {/* 加载状态 */}
                    {isLoadingSessions && (
                        <div className="flex items-center justify-center py-8">
                            <div className={`text-sm ${themeClasses.textSecondary}`}>加载中...</div>
                        </div>
                    )}

                    {/* 错误状态 */}
                    {sessionsError && (
                        <div className="flex flex-col items-center justify-center py-8">
                            <div className={`text-sm ${themeClasses.textSecondary} mb-2`}>加载失败</div>
                            <button
                                onClick={handleRefreshSessions}
                                className="text-xs text-blue-500 hover:text-blue-600 transition-colors duration-200"
                            >
                                重试
                            </button>
                        </div>
                    )}

                    {/* 会话列表 */}
                    {!isLoadingSessions && !sessionsError && (
                        <div className="space-y-1">
                            {sessions.length === 0 ? (
                                <div className={`text-sm ${themeClasses.textTertiary} text-center py-8`}>
                                    暂无对话记录
                                </div>
                            ) : (
                                sortedSessions.map((session: SessionData) => {
                                    const metadata = parseMetadata(session.metadata);
                                    const hasApp = metadata && metadata.app;
                                    const hasTools = metadata && metadata.tools && metadata.tools.length > 0;
                                    const isSelected = session.id === currentSessionId;

                                    return (
                                        <div
                                            key={session.id}
                                            className={`w-full text-left p-3 rounded-lg ${isSelected ? themeClasses.selected : themeClasses.hover} transition-colors duration-200 cursor-pointer group`}
                                            onClick={() => editingSessionId !== session.id && onSelectConversation(session.id.toString())}
                                        >
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1 min-w-0">
                                                    {editingSessionId === session.id ? (
                                                        <div className="flex items-center space-x-2">
                                                            <input
                                                                type="text"
                                                                value={editingTitle}
                                                                onChange={(e) => setEditingTitle(e.target.value)}
                                                                onKeyPress={handleTitleKeyPress}
                                                                onBlur={handleSaveEdit}
                                                                className={`text-xs font-medium ${themeClasses.text} bg-transparent border ${themeClasses.border} rounded px-1 py-0.5 flex-1 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                                                                autoFocus
                                                                disabled={isUpdatingTitle}
                                                            />
                                                            <div className="flex items-center space-x-1">
                                                                <button
                                                                    onClick={handleSaveEdit}
                                                                    disabled={isUpdatingTitle}
                                                                    className={`text-xs ${themeClasses.textSecondary} hover:text-green-500 transition-colors duration-200`}
                                                                    title="保存"
                                                                >
                                                                    ✓
                                                                </button>
                                                                <button
                                                                    onClick={handleCancelEdit}
                                                                    disabled={isUpdatingTitle}
                                                                    className={`text-xs ${themeClasses.textSecondary} hover:text-red-500 transition-colors duration-200`}
                                                                    title="取消"
                                                                >
                                                                    ×
                                                                </button>
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <h4 className={`text-xs font-medium ${themeClasses.text} truncate`}>
                                                            {session.sessionTitle}
                                                        </h4>
                                                    )}
                                                    {editingSessionId !== session.id && (
                                                        <div className="flex items-center space-x-2 mt-1.5">
                                                            {hasApp && (
                                                                <div className="flex items-center" title={`应用: ${metadata.app.name}`}>
                                                                    <span className={`text-[10px] ${themeClasses.textTertiary}`}>{metadata.app.name}</span>
                                                                </div>
                                                            )}
                                                            {hasTools && (
                                                                <div className="flex items-center" title="使用了工具">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                                                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0L7.86 5.89c-.38.22-.79.34-1.22.34H3.77c-1.66 0-2.62 2.05-1.52 3.4L4.2 11.7c.**********.18 1.28l-1.35 2.83c-.73 1.53.53 3.2 2.15 2.65l2.63-1.01c.42-.16.89-.16 1.32 0l2.63 1.01c1.62.55 2.88-1.12 2.15-2.65l-1.35-2.83a1.5 1.5 0 01.18-1.28l1.95-2.12c1.1-1.35.14-3.4-1.52-3.4h-2.87c-.43 0-.84-.12-1.22-.34L11.49 3.17z" clipRule="evenodd" />
                                                                    </svg>
                                                                </div>
                                                            )}
                                                            <p className={`text-[10px] ${themeClasses.textTertiary}`}>
                                                                {formatTime(session.updatedAt)}
                                                            </p>
                                                        </div>
                                                    )}
                                                </div>
                                                {editingSessionId !== session.id && (
                                                    <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-200 ml-2">
                                                        <button
                                                            className={`text-xs ${themeClasses.textSecondary} hover:text-blue-500 transition-colors duration-200`}
                                                            onClick={(e) => handleEditClick(e, session)}
                                                            title="编辑标题"
                                                        >
                                                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                            </svg>
                                                        </button>
                                                        <button
                                                            className={`text-xs ${themeClasses.textSecondary} hover:text-red-500 transition-colors duration-200`}
                                                            onClick={(e) => handleDeleteClick(e, session)}
                                                            title="删除对话"
                                                        >
                                                            ×
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )
                                })
                            )}
                        </div>
                    )}
                </div>
            </div>

            {/* 确认删除会话的模态框 */}
            <Modal
                isOpen={!!sessionToDelete}
                onClose={() => setSessionToDelete(null)}
                title="确认删除"
                size="sm"
                bgClass={isDarkMode ? "bg-gray-800" : "bg-white"}
            >
                <div>
                    <p className={isDarkMode ? "text-gray-300" : "text-gray-700"}>
                        您确定要删除对话 " <span className="font-semibold">{sessionToDelete?.sessionTitle}</span> " 吗？此操作无法撤销。
                    </p>
                    <div className="mt-6 flex justify-end space-x-3">
                        <button
                            onClick={() => setSessionToDelete(null)}
                            className={`px-4 py-2 rounded-lg text-sm font-medium ${isDarkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} transition-colors`}
                        >
                            取消
                        </button>
                        <button
                            onClick={handleConfirmDelete}
                            className="px-4 py-2 rounded-lg text-sm font-medium bg-red-600 text-white hover:bg-red-700 transition-colors"
                        >
                            删除
                        </button>
                    </div>
                </div>
            </Modal>

            {/* 确认新建对话的模态框 */}
            <Modal
                isOpen={isNewChatModalVisible}
                onClose={() => setIsNewChatModalVisible(false)}
                title="新建对话"
                size="sm"
                bgClass={isDarkMode ? "bg-gray-800" : "bg-white"}
            >
                <div>
                    <p className={isDarkMode ? "text-gray-300" : "text-gray-700"}>
                        您确定要开始一个新的对话吗？
                    </p>
                    <div className="mt-6 flex justify-end space-x-3">
                        <button
                            onClick={() => setIsNewChatModalVisible(false)}
                            className={`px-4 py-2 rounded-lg text-sm font-medium ${isDarkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} transition-colors`}
                        >
                            取消
                        </button>
                        <button
                            onClick={handleConfirmNewConversation}
                            className={`px-4 py-2 rounded-lg text-sm font-medium ${isDarkMode ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-blue-500 text-white hover:bg-blue-600'} transition-colors`}
                        >
                            确认
                        </button>
                    </div>
                </div>
            </Modal>


        </div>
    );
};

export default Sidebar; 