import React, { useState } from 'react';
import { useChatCompletion, useModels } from '../hooks/useOpenAI';
import { createUserMessage, formatOpenAIResponse, checkApiKey } from '../utils/openaiHelpers';

const OpenAIExample: React.FC = () => {
    const [prompt, setPrompt] = useState('');
    const [response, setResponse] = useState('');

    // 使用自定义hooks
    const chatMutation = useChatCompletion();
    const { data: models, isLoading: modelsLoading } = useModels();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!checkApiKey()) {
            alert('请先设置 REACT_APP_OPENAI_API_KEY 环境变量');
            return;
        }

        if (!prompt.trim()) return;

        try {
            const result = await chatMutation.mutateAsync({
                messages: [createUserMessage(prompt)],
                model: 'gpt-3.5-turbo',
                temperature: 0.2,
                max_tokens: 128000,
            });

            setResponse(formatOpenAIResponse(result));
        } catch (error) {
            console.error('OpenAI API 调用失败:', error);
            setResponse('API 调用失败，请检查您的API密钥和网络连接。');
        }
    };

    return (
        <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">OpenAI 集成示例</h2>

            {/* API Key 状态 */}
            <div className="mb-4 p-3 rounded-md bg-gray-100">
                <p className="text-sm text-gray-600">
                    API Key 状态: {checkApiKey() ?
                        <span className="text-green-600 font-semibold">已配置</span> :
                        <span className="text-red-600 font-semibold">未配置</span>
                    }
                </p>
            </div>

            {/* 模型列表 */}
            <div className="mb-4">
                <h3 className="text-lg font-semibold mb-2">可用模型</h3>
                {modelsLoading ? (
                    <p className="text-gray-500">加载中...</p>
                ) : (
                    <p className="text-sm text-gray-600">
                        找到 {models?.length || 0} 个模型
                    </p>
                )}
            </div>

            {/* 聊天界面 */}
            <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                    <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-2">
                        输入您的问题:
                    </label>
                    <textarea
                        id="prompt"
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows={4}
                        placeholder="请输入您想问的问题..."
                    />
                </div>

                <button
                    type="submit"
                    disabled={chatMutation.isPending || !prompt.trim()}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                    {chatMutation.isPending ? '处理中...' : '发送'}
                </button>
            </form>

            {/* 响应显示 */}
            {response && (
                <div className="mt-6 p-4 bg-gray-50 rounded-md">
                    <h3 className="text-lg font-semibold mb-2 text-gray-800">AI 回复:</h3>
                    <p className="text-gray-700 whitespace-pre-wrap">{response}</p>
                </div>
            )}

            {/* 错误处理 */}
            {chatMutation.isError && (
                <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md">
                    发生错误: {chatMutation.error?.message || '未知错误'}
                </div>
            )}
        </div>
    );
};

export default OpenAIExample; 