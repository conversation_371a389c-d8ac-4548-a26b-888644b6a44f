import { useMemo } from 'react';

export const useTheme = (isDarkMode: boolean) => {
    const themeColors = useMemo(() => ({
        // 主背景色
        mainBg: isDarkMode ? '#1f2937' : '#fafafa',
        // Header和工具栏背景色
        headerBg: isDarkMode ? '#374151' : '#f8f9fa',
        // 内容区背景色
        contentBg: isDarkMode ? '#111827' : 'white',
        // 主要文字颜色
        primaryText: isDarkMode ? '#f3f4f6' : '#374151',
        // 次要文字颜色
        secondaryText: isDarkMode ? '#9ca3af' : '#6b7280',
        // 淡化文字颜色
        mutedText: isDarkMode ? '#6b7280' : '#9ca3af',
        // 按钮hover背景
        buttonHover: isDarkMode ? '#4b5563' : '#f3f4f6',
        // 关闭按钮颜色
        closeIcon: isDarkMode ? '#d1d5db' : '#6b7280',
    }), [isDarkMode]);

    return themeColors;
}; 