import React from 'react';
import { useTheme } from './useTheme';
import { AttachmentContent } from './types';

interface AttachmentHeaderProps {
    isDarkMode: boolean;
    onClose: () => void;
    content: AttachmentContent;
}

const AttachmentHeader: React.FC<AttachmentHeaderProps> = ({ isDarkMode, onClose, content }) => {
    const themeColors = useTheme(isDarkMode);

    const getFileIcon = () => {
        switch (content.type) {
            case 'code':
                return (
                    <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                );
            case 'pdf':
                return (
                    <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
                    </svg>
                );
            case 'excel':
                return (
                    <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                    </svg>
                );
            default:
                return null;
        }
    };

    return (
        <div className="flex items-center justify-between px-4 py-2 flex-shrink-0 rounded-t-lg" style={{ backgroundColor: themeColors.headerBg }}>
            <div className="flex items-center space-x-2">
                {getFileIcon()}
                <h2 className="text-sm font-medium" style={{ color: themeColors.secondaryText }}>{content.title}</h2>
            </div>
            <button
                onClick={onClose}
                className="p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                style={{
                    color: themeColors.closeIcon,
                    transition: 'background-color 0.2s',
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = themeColors.buttonHover}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                aria-label="Close"
            >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
    );
};

export default AttachmentHeader; 