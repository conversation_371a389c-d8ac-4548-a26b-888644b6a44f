import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { useTheme } from '../common/useTheme';
import { AttachmentContent } from '../common/types';

// 配置 PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

interface PdfViewerProps {
    isDarkMode: boolean;
    content: AttachmentContent;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ isDarkMode, content }) => {
    const themeColors = useTheme(isDarkMode);
    const [numPages, setNumPages] = useState<number>(0);
    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pdfScale, setPdfScale] = useState<number>(1.0);
    const [viewMode, setViewMode] = useState<'scroll' | 'page'>('scroll');
    const [isAutoFit, setIsAutoFit] = useState<boolean>(true);
    const [containerWidth, setContainerWidth] = useState<number>(0);
    const containerRef = useRef<HTMLDivElement>(null);

    const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
        setNumPages(numPages);
        setPageNumber(1);
    }, []);

    const goToPrevPage = useCallback(() => {
        setPageNumber(prev => Math.max(prev - 1, 1));
    }, []);

    const goToNextPage = useCallback(() => {
        setPageNumber(prev => Math.min(prev + 1, numPages));
    }, [numPages]);

    const toggleViewMode = useCallback(() => {
        setViewMode(prev => prev === 'scroll' ? 'page' : 'scroll');
    }, []);

    const toggleAutoFit = useCallback(() => {
        setIsAutoFit(prev => !prev);
    }, []);

    // 防抖函数
    const debounce = useCallback((func: Function, delay: number) => {
        let timeoutId: NodeJS.Timeout;
        return (...args: any[]) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(null, args), delay);
        };
    }, []);

    useEffect(() => {
        const styleId = 'pdf-scrollbar-style';
        let existingStyle = document.getElementById(styleId);

        if (!existingStyle) {
            const style = document.createElement('style');
            style.id = styleId;
            document.head.appendChild(style);
            existingStyle = style;
        }

        const scrollbarColor = isDarkMode ? '#6b7280' : '#d1d5db';
        const trackColor = isDarkMode ? '#374151' : '#f3f4f6';

        existingStyle.textContent = `
            .custom-scrollbar::-webkit-scrollbar {
                width: 8px;
                height: 8px;
            }
            .custom-scrollbar::-webkit-scrollbar-track {
                background: ${trackColor};
                border-radius: 4px;
            }
            .custom-scrollbar::-webkit-scrollbar-thumb {
                background: ${scrollbarColor};
                border-radius: 4px;
                border: 1px solid ${trackColor};
            }
            .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                background: ${isDarkMode ? '#9ca3af' : '#9ca3af'};
            }
            .custom-scrollbar::-webkit-scrollbar-corner {
                background: ${trackColor};
            }
        `;
    }, [isDarkMode]);

    useEffect(() => {
        const debouncedUpdate = debounce(() => {
            if (containerRef.current) {
                setContainerWidth(containerRef.current.clientWidth);
            }
        }, 150);

        const resizeObserver = new ResizeObserver(debouncedUpdate);

        if (containerRef.current) {
            resizeObserver.observe(containerRef.current);
            setContainerWidth(containerRef.current.clientWidth);
        }

        return () => {
            resizeObserver.disconnect();
        };
    }, [debounce]);


    const handleZoomIn = useCallback(() => {
        if (!isAutoFit) {
            setPdfScale(prev => Math.min(prev + 0.2, 3.0));
        }
    }, [isAutoFit]);

    const handleZoomOut = useCallback(() => {
        if (!isAutoFit) {
            setPdfScale(prev => Math.max(prev - 0.2, 0.5));
        }
    }, [isAutoFit]);

    return (
        <div className="h-full flex flex-col">
            {/* PDF工具栏 */}
            <div className="flex items-center justify-between px-4 py-2 flex-shrink-0" style={{ backgroundColor: themeColors.headerBg }}>
                <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium" style={{ color: themeColors.secondaryText }}>
                        {viewMode === 'page' ? `第 ${pageNumber} 页，共 ${numPages} 页` : `共 ${numPages} 页`}
                    </span>
                    <span className="text-xs" style={{ color: themeColors.mutedText }}>
                        {isAutoFit ? '自适应宽度' : `缩放: ${Math.round(pdfScale * 100)}%`}
                    </span>
                    <span className="text-xs px-2 py-1 rounded"
                        style={{
                            backgroundColor: themeColors.buttonHover,
                            color: themeColors.mutedText
                        }}
                    >
                        {viewMode === 'scroll' ? '滚动模式' : '翻页模式'}
                    </span>
                </div>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={toggleViewMode}
                        className="p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        style={{
                            color: themeColors.secondaryText,
                            transition: 'background-color 0.2s',
                            backgroundColor: viewMode === 'scroll' ? themeColors.buttonHover : 'transparent'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = themeColors.buttonHover}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = viewMode === 'scroll' ? themeColors.buttonHover : 'transparent'}
                        title={viewMode === 'scroll' ? '切换到翻页模式' : '切换到滚动模式'}
                    >
                        {viewMode === 'scroll' ? (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        ) : (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                            </svg>
                        )}
                    </button>
                    <div className="w-px h-4" style={{ backgroundColor: themeColors.mutedText, opacity: 0.3 }}></div>
                    <button
                        onClick={toggleAutoFit}
                        className="p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        style={{
                            color: themeColors.secondaryText,
                            transition: 'background-color 0.2s',
                            backgroundColor: isAutoFit ? themeColors.buttonHover : 'transparent'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = themeColors.buttonHover}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = isAutoFit ? themeColors.buttonHover : 'transparent'}
                        title={isAutoFit ? '关闭自适应' : '开启自适应'}
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                        </svg>
                    </button>
                    <button
                        onClick={handleZoomOut}
                        disabled={pdfScale <= 0.5 || isAutoFit}
                        className="p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
                        style={{
                            color: themeColors.secondaryText,
                            transition: 'background-color 0.2s',
                        }}
                        onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = themeColors.buttonHover)}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                        title="缩小"
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
                        </svg>
                    </button>
                    <button
                        onClick={handleZoomIn}
                        disabled={pdfScale >= 3.0 || isAutoFit}
                        className="p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
                        style={{
                            color: themeColors.secondaryText,
                            transition: 'background-color 0.2s',
                        }}
                        onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = themeColors.buttonHover)}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                        title="放大"
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                    </button>
                    {viewMode === 'page' && (
                        <>
                            <div className="w-px h-4" style={{ backgroundColor: themeColors.mutedText, opacity: 0.3 }}></div>
                            <button
                                onClick={goToPrevPage}
                                disabled={pageNumber <= 1}
                                className="p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
                                style={{
                                    color: themeColors.secondaryText,
                                    transition: 'background-color 0.2s',
                                }}
                                onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = themeColors.buttonHover)}
                                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                                title="上一页"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                            </button>
                            <button
                                onClick={goToNextPage}
                                disabled={pageNumber >= numPages}
                                className="p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
                                style={{
                                    color: themeColors.secondaryText,
                                    transition: 'background-color 0.2s',
                                }}
                                onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = themeColors.buttonHover)}
                                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                                title="下一页"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </>
                    )}
                    {content.url && (
                        <a
                            href={content.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                            style={{
                                color: themeColors.secondaryText,
                                transition: 'background-color 0.2s',
                            }}
                            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = themeColors.buttonHover}
                            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                            title="在新窗口中打开"
                        >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M7 7l10 10M17 7l-4 4" />
                            </svg>
                        </a>
                    )}
                </div>
            </div>

            {/* PDF内容 */}
            <div
                ref={containerRef}
                className={`flex-1 overflow-auto p-4 ${viewMode === 'scroll' ? 'flex flex-col items-center space-y-4' : 'flex justify-center'} custom-scrollbar`}
                style={{
                    backgroundColor: themeColors.contentBg,
                    scrollbarWidth: 'thin',
                    scrollbarColor: `${isDarkMode ? '#6b7280' : '#d1d5db'} transparent`,
                }}
            >
                {content.url ? (
                    <Document
                        file={content.url}
                        onLoadSuccess={onDocumentLoadSuccess}
                        loading={
                            <div className="flex items-center justify-center h-64">
                                <div className="text-center">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-2"></div>
                                    <p style={{ color: themeColors.secondaryText }}>加载PDF中...</p>
                                </div>
                            </div>
                        }
                        error={
                            <div className="flex items-center justify-center h-64">
                                <div className="text-center">
                                    <svg className="w-16 h-16 mx-auto text-red-500 mb-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
                                    </svg>
                                    <h3 className="text-lg font-medium mb-2" style={{ color: themeColors.primaryText }}>PDF加载失败</h3>
                                    <p style={{ color: themeColors.secondaryText }}>无法加载PDF文件，请检查网络连接或文件路径</p>
                                </div>
                            </div>
                        }
                    >
                        {viewMode === 'scroll' ? (
                            Array.from({ length: numPages }, (_, index) => (
                                <div key={index + 1} className="relative">
                                    <div
                                        className="absolute -top-4 left-0 text-xs px-2 py-1 rounded"
                                        style={{
                                            backgroundColor: themeColors.headerBg,
                                            color: themeColors.mutedText
                                        }}
                                    >
                                        第 {index + 1} 页
                                    </div>
                                    <Page
                                        pageNumber={index + 1}
                                        scale={isAutoFit ? undefined : pdfScale}
                                        width={isAutoFit && containerWidth > 100 ? containerWidth - 80 : undefined}
                                        renderTextLayer={true}
                                        renderAnnotationLayer={true}
                                    />
                                </div>
                            ))
                        ) : (
                            <Page
                                pageNumber={pageNumber}
                                scale={isAutoFit ? undefined : pdfScale}
                                width={isAutoFit && containerWidth > 100 ? containerWidth - 80 : undefined}
                                renderTextLayer={true}
                                renderAnnotationLayer={true}
                            />
                        )}
                    </Document>
                ) : (
                    <div className="flex items-center justify-center h-64">
                        <div className="text-center">
                            <svg className="w-16 h-16 mx-auto text-red-500 mb-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
                            </svg>
                            <h3 className="text-lg font-medium mb-2" style={{ color: themeColors.primaryText }}>没有PDF文件</h3>
                            <p style={{ color: themeColors.secondaryText }}>请提供PDF文件URL</p>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default PdfViewer; 