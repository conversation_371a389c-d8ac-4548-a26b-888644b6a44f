import React from 'react';
import { useTheme } from '../common/useTheme';
import { AttachmentContent } from '../common/types';

interface PptViewerProps {
    isDarkMode: boolean;
    content: AttachmentContent;
}

const PptViewer: React.FC<PptViewerProps> = ({ isDarkMode, content }) => {
    const themeColors = useTheme(isDarkMode);

    return (
        <div className="h-full flex flex-col items-center justify-center mx-4 my-4 rounded-lg shadow-sm" style={{ backgroundColor: themeColors.contentBg }}>
            <div className="text-center">
                <svg className="w-16 h-16 mx-auto text-orange-500 mb-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M4,2H12.5L13,2.5V8.5L12.5,9H11V10.5H12.5L13,11V13.5L12.5,14H4A2,2 0 0,1 2,12V4A2,2 0 0,1 4,2M4,3A1,1 0 0,0 3,4V12A1,1 0 0,0 4,13H12V11.5H10.5L10,11V10H11.5L12,9.5V3H4M14,3.5V8H18.5L14,3.5M14.5,15H20A2,2 0 0,1 22,17V20A2,2 0 0,1 20,22H14.5L14,21.5V15.5L14.5,15M15,16V21H20A1,1 0 0,0 21,20V17A1,1 0 0,0 20,16H15M6,5H8V6H6V5M6,7H10V8H6V7Z" />
                </svg>
                <h3 className="text-lg font-medium mb-2" style={{ color: themeColors.primaryText }}>{content.title}</h3>
                <p className="mb-4" style={{ color: themeColors.secondaryText }}>PPT 查看器 (暂不支持在线预览)</p>
                {content.url && (
                    <a
                        href={content.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                        下载 PPT 文档
                        <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                    </a>
                )}
            </div>
        </div>
    );
};

export default PptViewer; 