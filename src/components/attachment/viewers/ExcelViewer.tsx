import React, { useState, useEffect, useRef } from 'react';
import * as XLSX from 'xlsx';
import Spreadsheet from 'x-data-spreadsheet';
import { useTheme } from '../common/useTheme';
import { AttachmentContent } from '../common/types';

import 'x-data-spreadsheet/dist/xspreadsheet.css';

const convertXlsxToXSpreadsheetData = (workbook: XLSX.WorkBook) => {
    const data: any[] = [];
    workbook.SheetNames.forEach(name => {
        const sheet = workbook.Sheets[name];
        const sheetData: { name: string; rows: { [key: number]: any } } = {
            name,
            rows: {}
        };

        const jsonData = XLSX.utils.sheet_to_json<any[]>(sheet, { header: 1 });

        jsonData.forEach((rowData, r) => {
            const row: { cells: { [key: number]: any } } = { cells: {} };
            rowData.forEach((cellData, c) => {
                row.cells[c] = { text: `${cellData || ''}` };
            });
            sheetData.rows[r] = row;
        });

        data.push(sheetData);
    });
    return data;
};

interface ExcelViewerProps {
    isDarkMode: boolean;
    content: AttachmentContent;
}

const ExcelViewer: React.FC<ExcelViewerProps> = ({ isDarkMode, content }) => {
    const themeColors = useTheme(isDarkMode);
    const spreadsheetContainer = useRef<HTMLDivElement>(null);
    const spreadsheetInstance = useRef<Spreadsheet | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!spreadsheetContainer.current) return;

        const loadExcelFile = async () => {
            if (!content.url) {
                setError("No file URL provided.");
                setIsLoading(false);
                return;
            }

            try {
                const response = await fetch(content.url);
                if (!response.ok) {
                    throw new Error(`Failed to fetch file: ${response.statusText}`);
                }
                const arrayBuffer = await response.arrayBuffer();
                const workbook = XLSX.read(arrayBuffer, { type: 'buffer' });

                const spreadsheetData = convertXlsxToXSpreadsheetData(workbook);

                if (spreadsheetContainer.current) {
                    // Clear previous instance if any
                    spreadsheetContainer.current.innerHTML = '';
                    spreadsheetInstance.current = new Spreadsheet(spreadsheetContainer.current, {
                        showToolbar: true,
                        showGrid: true,
                        showContextmenu: true,
                        view: {
                            height: () => spreadsheetContainer.current?.clientHeight || 0,
                            width: () => spreadsheetContainer.current?.clientWidth || 0,
                        },
                        row: {
                            len: spreadsheetData[0]?.rows ? Object.keys(spreadsheetData[0].rows).length + 20 : 100,
                            height: 25,
                        },
                    }).loadData(spreadsheetData);
                }

            } catch (err: any) {
                setError(`Error loading or parsing Excel file: ${err.message}`);
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        };

        loadExcelFile();

        // Cleanup function
        return () => {
            if (spreadsheetInstance.current) {
                // x-spreadsheet doesn't have a destroy method, so we clear the container
                if (spreadsheetContainer.current) {
                    spreadsheetContainer.current.innerHTML = '';
                }
                spreadsheetInstance.current = null;
            }
        };

    }, [content.url]);

    if (error) {
        return (
            <div className="h-full flex flex-col items-center justify-center p-4" style={{ backgroundColor: themeColors.contentBg }}>
                <h3 className="text-lg font-medium mb-2" style={{ color: themeColors.primaryText }}>{content.title}</h3>
                <p className="text-red-500">{error}</p>
                <p style={{ color: themeColors.secondaryText }}>Could not display the Excel file.</p>
            </div>
        );
    }

    return (
        <div className="h-full w-full flex flex-col" style={{ backgroundColor: themeColors.contentBg }}>
            <h3 className="text-lg font-medium p-4" style={{ color: themeColors.primaryText }}>{content.title}</h3>
            {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                    <p style={{ color: 'white' }}>Loading Excel data...</p>
                </div>
            )}
            <div ref={spreadsheetContainer} className="flex-grow w-full h-full"></div>
        </div>
    );
};

export default ExcelViewer; 