import React, { useState, useCallback, useEffect } from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneLight, oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { useTheme } from '../common/useTheme';
import { AttachmentContent } from '../common/types';
import Loading from '../../Loading';

interface CodeViewerProps {
    isDarkMode: boolean;
    content: AttachmentContent;
}

const CodeViewer: React.FC<CodeViewerProps> = ({ isDarkMode, content }) => {
    const themeColors = useTheme(isDarkMode);
    const [fileContent, setFileContent] = useState(content.content || '');
    const [isLoading, setIsLoading] = useState(!content.content && !!content.url);
    const [error, setError] = useState<string | null>(null);
    const [isEditing, setIsEditing] = useState(false);

    useEffect(() => {
        if (content.content) {
            setFileContent(content.content);
            setIsLoading(false);
            return;
        }

        if (content.url) {
            setIsLoading(true);
            setError(null);
            fetch(content.url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`文件加载失败: ${response.status} ${response.statusText}`);
                    }
                    return response.text();
                })
                .then(text => {
                    setFileContent(text);
                })
                .catch(e => {
                    console.error("加载文件内容失败:", e);
                    setError(e.message || "无法加载文件内容。");
                })
                .finally(() => {
                    setIsLoading(false);
                });
        }
    }, [content.url, content.content]);

    const handleContentChange = useCallback((value: string) => {
        setFileContent(value);
    }, []);

    const handleSave = useCallback(() => {
        // 这里可以添加保存逻辑
        console.log('保存内容:', fileContent);
        setIsEditing(false);
    }, [fileContent]);

    const handleCopy = useCallback(async () => {
        try {
            await navigator.clipboard.writeText(fileContent);
            // 可以添加成功提示
        } catch (err) {
            console.error('复制失败:', err);
        }
    }, [fileContent]);

    if (isLoading) {
        return (
            <div className="h-full flex flex-col items-center justify-center" style={{ backgroundColor: themeColors.contentBg }}>
                <Loading />
                <p className="mt-4 text-sm" style={{ color: themeColors.secondaryText }}>正在加载文件...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="h-full flex flex-col items-center justify-center p-4" style={{ backgroundColor: themeColors.contentBg }}>
                <div className="text-red-500 bg-red-100 p-4 rounded-lg text-center">
                    <p className="font-bold">加载错误</p>
                    <p className="text-sm mt-2">{error}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="h-full flex flex-col">
            {/* 工具栏 */}
            <div className="flex items-center justify-between px-4 py-2 flex-shrink-0" style={{ backgroundColor: themeColors.headerBg }}>
                <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium" style={{ color: themeColors.secondaryText }}>
                        {content.language || 'text'}
                    </span>
                    <span className="text-xs" style={{ color: themeColors.mutedText }}>
                        {fileContent.split('\n').length} 行
                    </span>
                </div>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={handleCopy}
                        className="p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        style={{
                            color: themeColors.secondaryText,
                            transition: 'background-color 0.2s',
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = themeColors.buttonHover}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                        title="复制代码"
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </button>
                    {content.isEditable && (
                        <>
                            <button
                                onClick={() => setIsEditing(!isEditing)}
                                className={`p-1.5 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${isEditing
                                    ? 'bg-purple-100 text-purple-600 hover:bg-purple-200'
                                    : ''
                                    }`}
                                style={!isEditing ? {
                                    color: themeColors.secondaryText,
                                    transition: 'background-color 0.2s',
                                } : {}}
                                onMouseEnter={!isEditing ? (e) => e.currentTarget.style.backgroundColor = themeColors.buttonHover : undefined}
                                onMouseLeave={!isEditing ? (e) => e.currentTarget.style.backgroundColor = 'transparent' : undefined}
                                title={isEditing ? "查看模式" : "编辑模式"}
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                            </button>
                            {isEditing && (
                                <button
                                    onClick={handleSave}
                                    className="p-1.5 rounded-md bg-green-100 text-green-600 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-500"
                                    title="保存"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                </button>
                            )}
                        </>
                    )}
                </div>
            </div>

            {/* 代码内容 */}
            <div className="flex-1 overflow-auto" style={{ backgroundColor: themeColors.contentBg }}>
                {isEditing ? (
                    <textarea
                        value={fileContent}
                        onChange={(e) => handleContentChange(e.target.value)}
                        className="w-full h-full p-4 resize-none focus:outline-none font-mono text-sm leading-6"
                        style={{
                            backgroundColor: themeColors.contentBg,
                            color: themeColors.primaryText,
                            fontFamily: "'SF Mono', Monaco, 'Fira Code', monospace"
                        }}
                    />
                ) : (
                    <SyntaxHighlighter
                        language={content.language || 'text'}
                        style={isDarkMode ? oneDark : oneLight}
                        wrapLongLines={true}
                        customStyle={{
                            margin: 0,
                            borderRadius: 0,
                            background: themeColors.contentBg,
                            fontSize: '14px',
                            lineHeight: '1.5'
                        }}
                        showLineNumbers
                        lineNumberStyle={{
                            color: themeColors.mutedText,
                            fontSize: '12px'
                        }}
                        codeTagProps={{
                            style: {
                                whiteSpace: 'pre-wrap',
                                wordBreak: 'break-all',
                                fontFamily: "'SF Mono', Monaco, 'Fira Code', monospace"
                            }
                        }}
                    >
                        {fileContent}
                    </SyntaxHighlighter>
                )}
            </div>
        </div>
    );
};

export default CodeViewer; 