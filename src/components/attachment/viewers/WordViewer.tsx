import React, { useState, useEffect } from 'react';
import mammoth from 'mammoth';
import { useTheme } from '../common/useTheme';
import { AttachmentContent } from '../common/types';
import Loading from '../../Loading';

interface WordViewerProps {
    isDarkMode: boolean;
    content: AttachmentContent;
}

const WordViewer: React.FC<WordViewerProps> = ({ isDarkMode, content }) => {
    const themeColors = useTheme(isDarkMode);
    const [htmlContent, setHtmlContent] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!content.url) {
            setError('没有提供文档URL。');
            setIsLoading(false);
            return;
        }

        setIsLoading(true);
        setError(null);
        setHtmlContent('');

        fetch(content.url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`网络错误: ${response.statusText}`);
                }
                return response.arrayBuffer();
            })
            .then(arrayBuffer => {
                return mammoth.convertToHtml({ arrayBuffer });
            })
            .then(result => {
                setHtmlContent(result.value);
            })
            .catch(err => {
                console.error("处理Word文档时出错:", err);
                setError('无法显示该文档。您可以尝试下载它。');
            })
            .finally(() => {
                setIsLoading(false);
            });

    }, [content.url]);

    const createMarkup = () => {
        return { __html: htmlContent };
    };

    return (
        <div className="h-full flex flex-col p-4" style={{ backgroundColor: themeColors.contentBg }}>
            <h3 className="text-lg font-medium mb-4 flex-shrink-0" style={{ color: themeColors.primaryText }}>{content.title}</h3>

            <div className="flex-grow overflow-auto rounded-lg shadow-inner" style={{ backgroundColor: isDarkMode ? '#1E1E1E' : '#FFFFFF', color: themeColors.primaryText }}>
                {isLoading && (
                    <div className="flex items-center justify-center h-full">
                        <Loading />
                    </div>
                )}

                {error && !isLoading && (
                    <div className="flex flex-col items-center justify-center h-full text-center p-4">
                        <p className="mb-4 text-red-500">{error}</p>
                        {content.url && (
                            <a
                                href={content.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                            >
                                下载 Word 文档
                                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </a>
                        )}
                    </div>
                )}

                {!isLoading && !error && htmlContent && (
                    <div className="p-6 prose prose-sm max-w-none" dangerouslySetInnerHTML={createMarkup()} />
                )}
            </div>
        </div>
    );
};

export default WordViewer; 