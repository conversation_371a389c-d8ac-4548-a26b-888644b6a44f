import React, { useState } from 'react';
import { AttachmentContent } from '../common/types';
import { useTheme } from '../common/useTheme';

interface ImageViewerProps {
    isDarkMode: boolean;
    content: AttachmentContent;
}

const ImageViewer: React.FC<ImageViewerProps> = ({ isDarkMode, content }) => {
    const themeColors = useTheme(isDarkMode);
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    const handleImageLoad = () => {
        setImageLoaded(true);
        setImageError(false);
    };

    const handleImageError = () => {
        setImageError(true);
        setImageLoaded(false);
    };

    return (
        <div className="h-full flex flex-col items-center justify-center p-4" style={{ backgroundColor: themeColors.contentBg }}>
            {content.url ? (
                <div className="max-w-full max-h-full flex items-center justify-center">
                    {!imageLoaded && !imageError && (
                        <div className="flex items-center justify-center">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-2"></div>
                                <p style={{ color: themeColors.secondaryText }}>加载图片中...</p>
                            </div>
                        </div>
                    )}
                    {imageError && (
                        <div className="text-center">
                            <svg className="w-16 h-16 mx-auto text-red-500 mb-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" />
                            </svg>
                            <h3 className="text-lg font-medium mb-2" style={{ color: themeColors.primaryText }}>图片加载失败</h3>
                            <p style={{ color: themeColors.secondaryText }}>无法加载图片，请检查网络连接或文件路径</p>
                        </div>
                    )}
                    <img
                        src={content.url}
                        alt={content.title}
                        className={`max-w-full max-h-full object-contain rounded-lg shadow-lg ${imageLoaded ? 'block' : 'hidden'}`}
                        onLoad={handleImageLoad}
                        onError={handleImageError}
                        style={{ maxHeight: 'calc(100vh - 200px)' }}
                    />
                </div>
            ) : (
                <div className="text-center">
                    <svg className="w-16 h-16 mx-auto text-red-500 mb-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" />
                    </svg>
                    <h3 className="text-lg font-medium mb-2" style={{ color: themeColors.primaryText }}>没有图片文件</h3>
                    <p style={{ color: themeColors.secondaryText }}>请提供图片文件URL</p>
                </div>
            )}
        </div>
    );
};

export default ImageViewer; 