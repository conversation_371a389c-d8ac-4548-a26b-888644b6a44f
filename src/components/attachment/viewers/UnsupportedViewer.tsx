import React from 'react';
import { useTheme } from '../common/useTheme';

interface UnsupportedViewerProps {
    isDarkMode: boolean;
}

const UnsupportedViewer: React.FC<UnsupportedViewerProps> = ({ isDarkMode }) => {
    const themeColors = useTheme(isDarkMode);

    return (
        <div className="h-full flex items-center justify-center">
            <p style={{ color: themeColors.secondaryText }}>暂不支持此文件类型</p>
        </div>
    );
};

export default UnsupportedViewer; 