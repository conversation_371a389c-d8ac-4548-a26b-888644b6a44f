import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import useAuth from '../hooks/useAuth';
import { DINGTALK_CONFIG } from '../utils/constants';
import LoginSuccessToast from './LoginSuccessToast';
import { UserInfoResponse } from '../types';

interface DingTalkCallbackProps {
    isDarkMode?: boolean;
}

const DingTalkCallback: React.FC<DingTalkCallbackProps> = ({ isDarkMode = false }) => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const { login, setUser } = useAuth();
    const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
    const [message, setMessage] = useState('正在处理登录信息...');
    const [showSuccessToast, setShowSuccessToast] = useState(false);

    useEffect(() => {
        const token = searchParams.get('token');
        const error = searchParams.get('error');
        const code = searchParams.get('code'); // 钉钉授权码
        const state = searchParams.get('state'); // 状态参数

        if (error) {
            setStatus('error');
            setMessage(`登录失败: ${decodeURIComponent(error)}`);
            // 3秒后跳转到首页
            setTimeout(() => {
                navigate('/');
            }, 3000);
        } else if (token) {
            // 直接传入的token（用于测试）
            try {
                login(token);
                // 登录成功后获取用户信息，直接传递token
                fetchUserInfo(token);
            } catch (err) {
                setStatus('error');
                setMessage('保存登录信息失败，请重试');
                setTimeout(() => {
                    navigate('/');
                }, 3000);
            }
        } else if (code) {
            // 处理钉钉OAuth授权码
            handleDingTalkAuth(code, state);
        } else {
            setStatus('error');
            setMessage('未获取到有效的登录信息');
            setTimeout(() => {
                navigate('/');
            }, 3000);
        }
    }, [searchParams, navigate]); // 移除 login 依赖项，避免无限循环

    const handleDingTalkAuth = async (authCode: string, state: string | null) => {
        try {
            setMessage('正在验证钉钉授权码...');

            // 调用后端API，传递authCode
            const response = await fetch(DINGTALK_CONFIG.AUTH_API, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    authCode: authCode,
                    state: state
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success && result.token) {
                // 登录成功
                login(result.token);
                // 登录成功后获取用户信息，直接传递token
                fetchUserInfo(result.token);
            } else {
                throw new Error(result.message || '登录失败');
            }
        } catch (err: any) {
            console.error('钉钉登录处理失败:', err);
            setStatus('error');
            setMessage(`登录处理失败: ${err.message}`);
            setTimeout(() => {
                navigate('/');
            }, 3000);
        }
    };

    // 获取用户信息 - 直接传递token参数，不依赖useAuth状态
    const fetchUserInfo = async (token: string) => {
        try {
            setMessage('正在获取用户信息...');

            const headers: Record<string, string> = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
            };

            const response = await fetch('/v1/api/user/info', {
                method: 'GET',
                headers,
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}, statusText: ${response.statusText}`);
            }

            const result: UserInfoResponse = await response.json();

            if (result.code === 200) {
                setUser(result.data);
                setStatus('success');
                setMessage('登录成功，正在跳转...');
                setShowSuccessToast(true);
            } else {
                throw new Error(result.message || '获取用户信息失败');
            }
        } catch (err: any) {
            console.error('获取用户信息失败:', err);
            setStatus('error');
            setMessage(`获取用户信息失败: ${err.message}`);
            setTimeout(() => {
                navigate('/');
            }, 3000);
        }
    };

    const handleSuccessComplete = () => {
        setShowSuccessToast(false);
        navigate('/');
    };

    const themeClasses = {
        container: isDarkMode
            ? 'bg-gray-900 text-white'
            : 'bg-gradient-to-br from-blue-50 to-indigo-100 text-gray-900',
        card: isDarkMode
            ? 'bg-gray-800 border-gray-700 shadow-2xl'
            : 'bg-white border-gray-200 shadow-2xl backdrop-blur-sm',
    };

    const getStatusColor = () => {
        switch (status) {
            case 'loading':
                return 'text-blue-600';
            case 'success':
                return 'text-green-600';
            case 'error':
                return 'text-red-600';
            default:
                return 'text-gray-500';
        }
    };

    const getIcon = () => {
        switch (status) {
            case 'loading':
                return (
                    <div className="relative">
                        <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600"></div>
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-blue-600 opacity-20 animate-pulse"></div>
                    </div>
                );
            case 'success':
                return (
                    <div className="relative">
                        <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center">
                            <svg className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-green-400 to-green-600 opacity-20 animate-pulse"></div>
                    </div>
                );
            case 'error':
                return (
                    <div className="relative">
                        <div className="h-16 w-16 bg-red-100 rounded-full flex items-center justify-center">
                            <svg className="h-10 w-10 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </div>
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red-400 to-red-600 opacity-20 animate-pulse"></div>
                    </div>
                );
        }
    };

    return (
        <>
            <div className={`min-h-screen flex items-center justify-center px-4 ${themeClasses.container}`}>
                <div className={`max-w-md w-full space-y-8 p-8 rounded-2xl border text-center ${themeClasses.card}`}>
                    <div className="flex justify-center mb-6">
                        {getIcon()}
                    </div>

                    <div className="space-y-4">
                        <h2 className="text-3xl font-bold mb-2">
                            钉钉登录处理
                        </h2>
                        <p className={`text-lg font-medium ${getStatusColor()}`}>
                            {message}
                        </p>
                    </div>

                    {status === 'loading' && (
                        <div className="mt-6">
                            <div className="flex justify-center space-x-1">
                                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                            </div>
                        </div>
                    )}

                    {status === 'error' && (
                        <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                            <div className="text-sm text-red-700 dark:text-red-300 space-y-2">
                                <p className="font-medium">发生了一些问题</p>
                                <p>页面将在3秒后自动跳转到首页</p>
                                <div className="mt-3">
                                    <button
                                        onClick={() => navigate('/')}
                                        className="inline-flex items-center px-3 py-1.5 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-red-900/20 dark:text-red-300 dark:border-red-700 dark:hover:bg-red-900/30"
                                    >
                                        立即返回首页
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {status === 'success' && (
                        <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                            <div className="text-sm text-green-700 dark:text-green-300 space-y-2">
                                <p className="font-medium">🎉 登录成功！</p>
                                <p>正在为您跳转到主页...</p>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* 登录成功提示 */}
            <LoginSuccessToast
                isVisible={showSuccessToast}
                onComplete={handleSuccessComplete}
                isDarkMode={isDarkMode}
            />
        </>
    );
};

export default DingTalkCallback; 