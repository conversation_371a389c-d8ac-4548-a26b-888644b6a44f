import React, { useEffect, useState } from 'react';

interface LoginSuccessToastProps {
    isVisible: boolean;
    onComplete?: () => void;
    isDarkMode?: boolean;
}

const LoginSuccessToast: React.FC<LoginSuccessToastProps> = ({
    isVisible,
    onComplete,
    isDarkMode = false
}) => {
    const [progress, setProgress] = useState(0);

    useEffect(() => {
        if (isVisible) {
            const timer = setInterval(() => {
                setProgress(prev => {
                    if (prev >= 100) {
                        clearInterval(timer);
                        setTimeout(() => {
                            onComplete?.();
                        }, 200);
                        return 100;
                    }
                    return prev + 2;
                });
            }, 20); // 1秒内完成进度条

            return () => clearInterval(timer);
        }
    }, [isVisible, onComplete]);

    if (!isVisible) return null;

    return (
        <div className="fixed inset-0 z-[60] flex items-center justify-center p-4">
            {/* 背景遮罩 */}
            <div className="absolute inset-0 bg-black/30 backdrop-blur-sm" />

            {/* 成功提示卡片 */}
            <div className={`relative ${isDarkMode
                ? 'bg-gray-800 border-gray-700'
                : 'bg-white border-gray-200'
                } rounded-2xl shadow-2xl border p-8 max-w-sm w-full mx-auto transform transition-all duration-500 animate-bounce`}>

                {/* 成功图标 */}
                <div className="text-center mb-6">
                    <div className="relative inline-flex items-center justify-center w-20 h-20 mb-4">
                        {/* 外圈动画 */}
                        <div className="absolute inset-0 w-20 h-20 rounded-full bg-green-500 opacity-25 animate-ping"></div>
                        {/* 内圈背景 */}
                        <div className="relative w-16 h-16 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                            {/* 勾选图标 */}
                            <svg
                                className="w-8 h-8 text-white"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                    </div>

                    {/* 成功文字 */}
                    <h3 className={`text-xl font-bold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'
                        }`}>
                        登录成功！
                    </h3>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'
                        }`}>
                        欢迎回来，正在为您跳转...
                    </p>
                </div>

                {/* 进度条 */}
                <div className={`w-full h-1 rounded-full overflow-hidden ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                    }`}>
                    <div
                        className="h-full bg-gradient-to-r from-green-500 to-emerald-500 rounded-full transition-all duration-100 ease-out"
                        style={{ width: `${progress}%` }}
                    />
                </div>

                {/* 进度文字 */}
                <div className="text-center mt-3">
                    <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                        {Math.round(progress)}%
                    </span>
                </div>
            </div>

        </div>
    );
};

export default LoginSuccessToast; 