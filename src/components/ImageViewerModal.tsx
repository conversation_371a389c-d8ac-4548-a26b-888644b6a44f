import React, { useState, useEffect, useRef } from 'react';
import { X, Copy, Download, Check, ChevronDown, ChevronUp, ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import type { MediaItem } from '../pages/Library';

interface ImageViewerModalProps {
    items: MediaItem[];
    currentIndex: number;
    isDarkMode: boolean;
    onClose: () => void;
    onNavigate: (newIndex: number) => void;
}

const ImageViewerModal: React.FC<ImageViewerModalProps> = ({ items, currentIndex, isDarkMode, onClose, onNavigate }) => {
    const [copied, setCopied] = useState(false);
    const [isDownloading, setIsDownloading] = useState(false);
    const [isPromptExpanded, setIsPromptExpanded] = useState(false);

    // 缩放和拖拽相关状态
    const [scale, setScale] = useState(1);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });

    const imageRef = useRef<HTMLImageElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    const item = items[currentIndex];

    // 重置缩放和位置
    const resetTransform = () => {
        setScale(1);
        setPosition({ x: 0, y: 0 });
    };

    // 当图片切换时重置变换
    useEffect(() => {
        resetTransform();
    }, [currentIndex]);

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                onClose();
            } else if (e.key === 'ArrowRight') {
                if (currentIndex < items.length - 1) {
                    onNavigate(currentIndex + 1);
                }
            } else if (e.key === 'ArrowLeft') {
                if (currentIndex > 0) {
                    onNavigate(currentIndex - 1);
                }
            } else if (e.key === '0' || e.key === 'r') {
                // 按0或r键重置缩放
                resetTransform();
            }
        };
        window.addEventListener('keydown', handleKeyDown);
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [onClose, onNavigate, currentIndex, items.length]);

    // 鼠标滚轮缩放
    useEffect(() => {
        const handleWheel = (e: WheelEvent) => {
            if (!containerRef.current || !containerRef.current.contains(e.target as Node)) return;

            e.preventDefault();
            const delta = e.deltaY > 0 ? -0.1 : 0.1;
            const newScale = Math.max(0.5, Math.min(5, scale + delta));

            // 计算缩放中心点
            const rect = containerRef.current.getBoundingClientRect();
            const centerX = e.clientX - rect.left - rect.width / 2;
            const centerY = e.clientY - rect.top - rect.height / 2;

            // 调整位置以保持鼠标点为缩放中心
            const scaleRatio = newScale / scale;
            const newX = position.x - centerX * (scaleRatio - 1);
            const newY = position.y - centerY * (scaleRatio - 1);

            setScale(newScale);
            setPosition({ x: newX, y: newY });
        };

        const container = containerRef.current;
        if (container) {
            container.addEventListener('wheel', handleWheel, { passive: false });
            return () => {
                container.removeEventListener('wheel', handleWheel);
            };
        }
    }, [scale, position]);

    if (!item) return null;

    const handleCopy = () => {
        if (!item.prompt) return;

        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(item.prompt).then(() => {
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        } else {
            // Fallback for insecure contexts or older browsers
            const textArea = document.createElement("textarea");
            textArea.value = item.prompt;
            textArea.style.position = "fixed";
            textArea.style.left = "-9999px";
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            } catch (err) {
                console.error('Fallback: Failed to copy', err);
            }
            document.body.removeChild(textArea);
        }
    };

    const handleDownload = async () => {
        if (isDownloading) return;
        setIsDownloading(true);
        try {
            const response = await fetch(item.url);
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            const filename = item.url.substring(item.url.lastIndexOf('/') + 1) || `${item.id}.png`;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error("Failed to download image:", error);
            window.open(item.url, '_blank');
        } finally {
            setIsDownloading(false);
        }
    };

    const handleNext = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (currentIndex < items.length - 1) {
            onNavigate(currentIndex + 1);
        }
    };

    const handlePrevious = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (currentIndex > 0) {
            onNavigate(currentIndex - 1);
        }
    };

    // 缩放控制
    const handleZoomIn = () => {
        const newScale = Math.min(5, scale + 0.2);
        setScale(newScale);
    };

    const handleZoomOut = () => {
        const newScale = Math.max(0.5, scale - 0.2);
        setScale(newScale);

        // 如果缩小到1倍以下，重置位置
        if (newScale <= 1) {
            setPosition({ x: 0, y: 0 });
        }
    };

    // 双击缩放
    const handleDoubleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        if (scale === 1) {
            // 放大到2倍
            setScale(2);
            // 计算双击点为缩放中心
            const rect = containerRef.current?.getBoundingClientRect();
            if (rect) {
                const centerX = e.clientX - rect.left - rect.width / 2;
                const centerY = e.clientY - rect.top - rect.height / 2;
                setPosition({ x: -centerX, y: -centerY });
            }
        } else {
            // 重置到1倍
            resetTransform();
        }
    };

    // 拖拽开始
    const handleMouseDown = (e: React.MouseEvent) => {
        if (scale <= 1) return; // 只有在放大状态下才能拖拽

        e.preventDefault();
        setIsDragging(true);
        setDragStart({ x: e.clientX, y: e.clientY });
        setLastPanPoint({ x: position.x, y: position.y });
    };

    // 拖拽过程
    const handleMouseMove = (e: React.MouseEvent) => {
        if (!isDragging || scale <= 1) return;

        e.preventDefault();
        const deltaX = e.clientX - dragStart.x;
        const deltaY = e.clientY - dragStart.y;

        setPosition({
            x: lastPanPoint.x + deltaX,
            y: lastPanPoint.y + deltaY
        });
    };

    // 拖拽结束
    const handleMouseUp = () => {
        setIsDragging(false);
    };

    // 解析图片比例
    const parseAspectRatio = (sizeStr: string | undefined): { width: number; height: number; isPortrait: boolean; isLandscape: boolean; isSquare: boolean } => {
        if (!sizeStr) return { width: 1, height: 1, isPortrait: false, isLandscape: false, isSquare: true };

        const [widthStr, heightStr] = sizeStr.split(':');
        const width = parseInt(widthStr) || 1;
        const height = parseInt(heightStr) || 1;

        return {
            width,
            height,
            isPortrait: height > width,
            isLandscape: width > height,
            isSquare: width === height
        };
    };

    const { width: aspectWidth, height: aspectHeight, isPortrait, isLandscape, isSquare } = parseAspectRatio(item.size);
    const aspectRatio = `${aspectWidth} / ${aspectHeight}`;

    // 根据图片比例计算合适的显示尺寸（仅用于初始尺寸计算）
    const getInitialImageSize = () => {
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        if (isSquare) {
            // 正方形图片
            const size = Math.min(viewportWidth * 0.6, viewportHeight * 0.6);
            return { width: size, height: size };
        } else if (isPortrait) {
            // 竖向图片 (如 3:4, 9:16)
            const maxHeight = viewportHeight * 0.7;
            const maxWidth = viewportWidth * 0.5;
            const ratio = aspectWidth / aspectHeight;
            const widthByHeight = maxHeight * ratio;

            if (widthByHeight <= maxWidth) {
                return { width: widthByHeight, height: maxHeight };
            } else {
                return { width: maxWidth, height: maxWidth / ratio };
            }
        } else {
            // 横向图片 (如 4:3, 16:9)
            const maxWidth = viewportWidth * 0.8;
            const maxHeight = viewportHeight * 0.6;
            const ratio = aspectWidth / aspectHeight;
            const heightByWidth = maxWidth / ratio;

            if (heightByWidth <= maxHeight) {
                return { width: maxWidth, height: heightByWidth };
            } else {
                return { width: maxHeight * ratio, height: maxHeight };
            }
        }
    };

    const initialSize = getInitialImageSize();

    return (
        <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center p-4 transition-opacity duration-300" onClick={onClose}>
            <div className="relative w-full h-full flex items-center justify-center overflow-hidden" onClick={(e) => e.stopPropagation()}>

                {/* 导航按钮 */}
                <button
                    onClick={handlePrevious}
                    disabled={currentIndex === 0}
                    className="absolute left-4 top-1/2 -translate-y-1/2 p-2 rounded-full z-30 bg-black/30 text-white/80 hover:bg-black/50 hover:text-white transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
                >
                    <ChevronLeft size={32} />
                </button>

                <button
                    onClick={handleNext}
                    disabled={currentIndex === items.length - 1}
                    className="absolute right-4 top-1/2 -translate-y-1/2 p-2 rounded-full z-30 bg-black/30 text-white/80 hover:bg-black/50 hover:text-white transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
                >
                    <ChevronRight size={32} />
                </button>

                {/* 缩放控制按钮 */}
                <div className="absolute top-4 left-4 flex flex-col space-y-2 z-30">
                    <button
                        onClick={handleZoomIn}
                        className="p-2 rounded-full bg-black/30 text-white/80 hover:bg-black/50 hover:text-white transition-colors"
                        title="放大 (滚轮向上)"
                    >
                        <ZoomIn size={20} />
                    </button>
                    <button
                        onClick={handleZoomOut}
                        className="p-2 rounded-full bg-black/30 text-white/80 hover:bg-black/50 hover:text-white transition-colors"
                        title="缩小 (滚轮向下)"
                    >
                        <ZoomOut size={20} />
                    </button>
                    <button
                        onClick={resetTransform}
                        className="p-2 rounded-full bg-black/30 text-white/80 hover:bg-black/50 hover:text-white transition-colors"
                        title="重置缩放 (按0键或R键)"
                    >
                        <RotateCcw size={20} />
                    </button>
                    <div className="px-2 py-1 rounded bg-black/30 text-white/80 text-xs text-center min-w-[60px]">
                        {Math.round(scale * 100)}%
                    </div>
                </div>

                {/* 图片容器 - 允许图片超出边界 */}
                <div
                    ref={containerRef}
                    className="absolute inset-0 flex items-center justify-center"
                    style={{
                        cursor: scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default'
                    }}
                    onDoubleClick={handleDoubleClick}
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    onMouseLeave={handleMouseUp}
                >
                    <img
                        ref={imageRef}
                        src={item.url}
                        alt={item.prompt || 'Generated image'}
                        className="object-contain transition-transform duration-200 ease-out select-none"
                        style={{
                            width: `${initialSize.width}px`,
                            height: `${initialSize.height}px`,
                            transform: `scale(${scale}) translate(${position.x}px, ${position.y}px)`,
                            transformOrigin: 'center'
                        }}
                        draggable={false}
                    />
                </div>

                {/* 关闭按钮 */}
                <button onClick={onClose} className="absolute top-4 right-4 p-2 rounded-full z-20 bg-black/30 text-white/80 hover:bg-black/50 hover:text-white transition-colors">
                    <X size={24} />
                </button>

                {/* 提示词区域 */}
                {item.prompt && (
                    <div className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 text-white transition-all duration-300 ease-in-out ${isPromptExpanded ? 'max-h-[40vh]' : 'max-h-40'}`}>
                        <div className="max-w-4xl mx-auto h-full flex flex-col">
                            <div className={`overflow-y-auto pr-4`}>
                                <p className={`text-sm md:text-base leading-relaxed whitespace-pre-wrap ${!isPromptExpanded ? 'line-clamp-2' : ''}`}>
                                    {item.prompt}
                                </p>
                            </div>

                            <div className="mt-4 flex-shrink-0 flex justify-between items-center">
                                <div className="flex items-center space-x-4">
                                    <button onClick={() => setIsPromptExpanded(!isPromptExpanded)} className="flex items-center text-xs font-semibold tracking-wider uppercase text-white/80 hover:text-white transition-colors">
                                        {isPromptExpanded ? <ChevronUp size={16} className="mr-1" /> : <ChevronDown size={16} className="mr-1" />}
                                        <span>{isPromptExpanded ? '收起' : '展开'}</span>
                                    </button>
                                    {item.modelUsed && (
                                        <div className="bg-white/10 backdrop-blur-sm text-white/80 text-xs font-medium rounded-md px-2 py-1">
                                            {item.modelUsed}
                                        </div>
                                    )}
                                </div>

                                <div className="flex items-center space-x-4">
                                    <button onClick={handleCopy} title="复制提示词" className="text-white/80 hover:text-white transition-colors">
                                        {copied ? <Check size={20} className="text-green-500" /> : <Copy size={20} />}
                                    </button>
                                    <button onClick={handleDownload} disabled={isDownloading} title="下载图片" className="text-white/80 hover:text-white transition-colors disabled:opacity-50">
                                        <Download size={20} />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ImageViewerModal; 