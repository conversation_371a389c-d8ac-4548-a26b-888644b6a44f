import React from 'react';
import useAuth from '../hooks/useAuth';

interface HeaderProps {
    title: string;
    subtitle?: string;
}

const Header: React.FC<HeaderProps> = ({ title, subtitle }) => {
    const { userInfo, logout } = useAuth();

    const handleLogout = () => {
        logout();
        // 刷新页面，确保AuthGuard重新检查认证状态
        setTimeout(() => {
            window.location.reload();
        }, 100);
    };
    return (
        <header className="bg-white shadow-sm border-b border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center py-1">
                    <div className="flex items-center">
                        <h1 className="text-lg font-bold text-gray-900">{title}</h1>
                        {subtitle && (
                            <span className="ml-2 text-xs text-gray-500">{subtitle}</span>
                        )}
                    </div>
                    {/* 用户信息区域 */}
                    <div className="flex items-center space-x-2">
                        {userInfo && (
                            <div className="flex items-center space-x-2">
                                {/* 用户头像 */}
                                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                    {userInfo.avatar ? (
                                        <img
                                            src={userInfo.avatar}
                                            alt={userInfo.actualName}
                                            className="w-6 h-6 rounded-full object-cover"
                                        />
                                    ) : (
                                        <span className="text-white text-xs font-medium">
                                            {userInfo.actualName.charAt(0)}
                                        </span>
                                    )}
                                </div>

                                {/* 用户信息 */}
                                <div className="hidden md:block">
                                    <p className="text-xs font-medium text-gray-900">{userInfo.actualName}</p>
                                    <p className="text-xs text-gray-500">{userInfo.remark || userInfo.email}</p>
                                </div>

                                {/* 退出按钮 */}
                                <button
                                    onClick={handleLogout}
                                    className="text-gray-500 hover:text-red-500 transition-colors duration-200 p-0.5 rounded"
                                    title="退出登录"
                                >
                                    <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                    </svg>
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header; 