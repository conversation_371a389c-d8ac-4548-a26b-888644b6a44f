import React, { useState } from 'react';
import { BrowserRouter as Router, useNavigate, useLocation } from 'react-router-dom';
import './App.css';
import AnimatedRoutes from './components/AnimatedRoutes';
import Sidebar from './components/Sidebar';
import AuthGuard from './components/AuthGuard';
import { ChatHeader } from './components/chat';
import useAuth from './hooks/useAuth';
import { useSession } from './hooks';
import { Toaster } from 'react-hot-toast';

const AppContent: React.FC = () => {
    const [sidebarVisible, setSidebarVisible] = useState(true);
    const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
    const [isDarkMode, setIsDarkMode] = useState(false);
    const navigate = useNavigate();
    const location = useLocation();
    const { isAuthenticated } = useAuth();
    const { createNewSession, isLoading: isCreatingSession } = useSession();

    // 检查是否是钉钉回调页面，如果是则不显示认证保护
    const isDingTalkCallback = location.pathname === '/dingtalk-callback';

    const toggleSidebar = () => {
        setSidebarVisible(!sidebarVisible);
    };

    const toggleTheme = () => {
        setIsDarkMode(!isDarkMode);
    };

    const handleSelectConversation = (id: string) => {
        setSelectedConversationId(id);
        // 可以在这里加载对应的对话历史
        console.log('Selected conversation:', id, 'Current selected:', selectedConversationId);
        // 导航到对应的会话路由
        navigate(`/chat/${id}`);
    };

    const handleNewConversation = async () => {
        try {
            console.log('从侧边栏创建新对话...');
            setSelectedConversationId(null);

            // 调用创建会话API
            const sessionResponse = await createNewSession({
                sessionTitle: '新建对话',
                modelUsed: 'deepseek-ai/DeepSeek-V3',
                metadata: JSON.stringify({
                    settings: { temperature: 0.2, maxTokens: 128000 }
                })
            });

            console.log('侧边栏新对话创建成功:', sessionResponse.sessionId);

            // 导航到聊天界面
            navigate(`/chat/${sessionResponse.sessionId}`);
        } catch (error) {
            console.error('创建新对话失败:', error);
            // 即使创建会话失败，也导航到聊天界面
            navigate('/');
        }
    };

    const handleNavigateToApps = () => {
        navigate('/apps');
    };

    const handleNavigateToKnowledge = () => {
        navigate('/knowledge');
    };

    const handleNavigateToVisualTop = () => {
        navigate('/visual-model/top');
    };

    const handleNavigateToLibrary = () => {
        navigate('/library');
    };

    const handleNavigateToImages = () => {
        navigate('/images');
    };

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-900'
    };

    const appContent = (
        <div className={`flex h-screen ${themeClasses.background} ${themeClasses.text} overflow-hidden`}>
            <Toaster position="top-center" reverseOrder={false} />
            {/* 左侧边栏 - 添加过渡动画 */}
            <div className={`
                ${sidebarVisible ? 'w-80' : 'w-0'} 
                flex-shrink-0 
                transition-all duration-300 ease-in-out 
                overflow-hidden
            `}>
                <div className="w-80 h-full">
                    <Sidebar
                        visible={sidebarVisible}
                        isDarkMode={isDarkMode}
                        onToggleTheme={toggleTheme}
                        currentSessionId={selectedConversationId}
                        onSelectConversation={handleSelectConversation}
                        onNewConversation={handleNewConversation}
                        onNavigateToApps={handleNavigateToApps}
                        onNavigateToKnowledge={handleNavigateToKnowledge}
                        onNavigateToVisualTop={handleNavigateToVisualTop}
                        onNavigateToLibrary={handleNavigateToLibrary}
                        onNavigateToImages={handleNavigateToImages}
                        pathname={location.pathname}
                    />
                </div>
            </div>

            {/* 主要内容区域 - 使用动画路由组件 */}
            <div className="flex-1 flex flex-col min-w-0 transition-all duration-300 ease-in-out">
                {/* 全局Header */}
                <div className="flex-shrink-0">
                    <ChatHeader
                        sidebarVisible={sidebarVisible}
                        onToggleSidebar={toggleSidebar}
                        onToggleTheme={toggleTheme}
                        isDarkMode={isDarkMode}
                    />
                </div>

                {/* 页面内容 */}
                <div className="flex-1 min-h-0">
                    <AnimatedRoutes
                        sidebarVisible={sidebarVisible}
                        isDarkMode={isDarkMode}
                        onToggleSidebar={toggleSidebar}
                        onToggleTheme={toggleTheme}
                    />
                </div>
            </div>
        </div>
    );

    // 如果是钉钉回调页面，直接返回内容不使用AuthGuard
    if (isDingTalkCallback) {
        return appContent;
    }

    // 其他页面使用AuthGuard包装
    return (
        <AuthGuard isDarkMode={isDarkMode}>
            {appContent}
        </AuthGuard>
    );
};

function App() {
    return (
        <Router>
            <AppContent />
        </Router>
    );
}

export default App; 