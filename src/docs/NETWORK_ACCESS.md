# 局域网访问配置说明

## 🌐 配置概述

本应用已配置支持局域网访问，其他设备可以通过局域网IP访问应用。

## 📋 当前配置

### 开发环境 (.env)
```bash
REACT_APP_ENV=development
REACT_APP_API_BASE_URL=/api                                    # 使用代理
REACT_APP_DINGTALK_AUTH_API=/v1/api/ddlogin/auth              # 使用v1代理
REACT_APP_DINGTALK_CALLBACK_URL=http://**************:3000/dingtalk-callback
REACT_APP_BACKEND_URL=http://**************:8080              # 代理目标
HOST=0.0.0.0                                                  # 允许局域网访问
```

### 生产环境 (.env.local)
```bash
REACT_APP_ENV=production
REACT_APP_API_BASE_URL=http://**************:8080/api         # 直接访问后端
REACT_APP_DINGTALK_AUTH_API=/v1/api/ddlogin/auth
REACT_APP_DINGTALK_CALLBACK_URL=http://**************:3000/dingtalk-callback
HOST=0.0.0.0                                                  # 允许局域网访问
```

## 🚀 使用方法

### 开发环境启动
```bash
npm start
```

应用将在以下地址可访问：
- **本机访问**: `http://localhost:3000`
- **局域网访问**: `http://**************:3000`

### 局域网设备访问
1. 确保设备在同一局域网内
2. 在浏览器中输入：`http://**************:3000`
3. 可以正常使用所有功能，包括钉钉登录

## 🔧 功能验证

### 1. 本机访问测试
```bash
curl -I http://localhost:3000
# 应返回: HTTP/1.1 200 OK
```

### 2. 局域网访问测试
```bash
curl -I http://**************:3000
# 应返回: HTTP/1.1 200 OK
```

### 3. 钉钉登录测试
- 访问: `http://**************:3000/login`
- 点击钉钉登录按钮
- 应该正常跳转到钉钉授权页面

### 4. API代理测试
```bash
# 钉钉登录API代理测试（使用v1代理）
curl -I http://**************:3000/v1/api/ddlogin/auth
# 应返回: HTTP/1.1 400 Bad Request (来自后端，缺少参数)
```

## 📱 移动设备访问

### iOS/Android设备
1. 连接到相同的WiFi网络
2. 打开浏览器输入：`http://**************:3000`
3. 可以正常使用所有功能

### 平板电脑访问
- 支持响应式设计，在平板上有良好的显示效果
- 支持触摸操作

## 🔒 安全注意事项

1. **防火墙设置**: 确保3000端口在防火墙中开放
2. **网络安全**: 仅在可信的局域网环境中使用
3. **HTTPS**: 生产环境建议配置HTTPS

## 🛠️ 故障排除

### 1. 无法访问局域网地址
- 检查防火墙设置
- 确认HOST=0.0.0.0配置是否生效
- 确认设备在同一网络

### 2. 钉钉登录失败
- 检查回调URL是否正确配置
- 确认后端服务是否可访问

### 3. API请求失败
- 开发环境：检查代理配置
- 生产环境：检查直接API地址是否可访问

## 📋 配置清单

- [x] HOST=0.0.0.0 配置完成
- [x] 局域网IP地址确认：**************
- [x] 开发环境代理配置
- [x] 生产环境直连配置
- [x] 钉钉回调URL配置
- [x] 跨域问题解决

## 💡 使用建议

1. **开发调试**: 使用局域网访问在多设备上测试
2. **团队协作**: 团队成员可以访问开发中的应用
3. **移动测试**: 在真实移动设备上测试用户体验
4. **演示展示**: 可以在会议中展示给其他人

## 🔄 环境切换

### 使用开发环境
```bash
# 删除 .env.local 或重命名为 .env.local.bak
mv .env.local .env.local.bak
npm start
```

### 使用生产环境
```bash
# 确保 .env.local 存在
npm start
``` 