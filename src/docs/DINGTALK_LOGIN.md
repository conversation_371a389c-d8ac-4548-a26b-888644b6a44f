# 钉钉登录集成说明

## 概述

本项目集成了钉钉登录功能，用户可以通过钉钉账号快速登录系统。支持开发和生产环境的不同配置。

## 环境配置

### 1. 开发环境配置 (.env)
```bash
REACT_APP_ENV=development
REACT_APP_API_BASE_URL=/api
REACT_APP_DINGTALK_AUTH_API=/v1/api/ddlogin/auth
REACT_APP_DINGTALK_CALLBACK_URL=http://**************:3000/dingtalk-callback
REACT_APP_BACKEND_URL=http://**************:8080
```

### 2. 生产环境配置 (.env.local)
```bash
REACT_APP_ENV=production
REACT_APP_API_BASE_URL=http://**************:8080/api
REACT_APP_DINGTALK_AUTH_API=/v1/api/ddlogin/auth
REACT_APP_DINGTALK_CALLBACK_URL=http://**************:3000/dingtalk-callback
REACT_APP_BACKEND_URL=http://**************:8080
```

### 3. 代理配置
开发环境使用了代理配置来解决跨域问题：
- `/api` 路径代理到后端服务器
- `/v1` 路径代理到后端服务器（OpenAI相关及钉钉登录）

## 配置说明

### 前端配置
在 `src/utils/constants.ts` 中的配置会自动读取环境变量：
```typescript
export const DINGTALK_CONFIG = {
    AUTH_API: process.env.REACT_APP_DINGTALK_AUTH_API || '/v1/api/ddlogin/auth',
    CALLBACK_URL: process.env.REACT_APP_DINGTALK_CALLBACK_URL || `${window.location.origin}/dingtalk-callback`,
} as const;
```

## 使用流程

### 1. 用户登录
1. 用户访问应用时，如果未登录会自动重定向到 `/login` 页面
2. 用户点击"钉钉登录"按钮
3. 系统通过代理跳转到钉钉授权页面（开发环境）或直接跳转（生产环境）
4. 用户在钉钉中确认授权

### 2. 登录回调处理
1. 钉钉授权成功后，后端处理登录逻辑
2. 后端重定向到 `/dingtalk-callback?token=xxx` 或 `/dingtalk-callback?error=xxx`
3. 前端回调页面处理token或错误信息
4. 成功时保存token并跳转到主页，失败时显示错误信息

### 3. Token管理
- Token保存在localStorage中，key为 `dingtalk_token`
- 使用 `useAuth` hook管理登录状态
- 提供登录、登出、获取认证头等功能

## 主要组件

### 1. DingTalkLogin 组件
- 位置：`src/components/DingTalkLogin.tsx`
- 功能：显示登录界面，处理跳转到钉钉授权

### 2. DingTalkCallback 组件
- 位置：`src/components/DingTalkCallback.tsx`
- 功能：处理钉钉登录回调，保存token或显示错误

### 3. useAuth Hook
- 位置：`src/hooks/useAuth.ts`
- 功能：管理登录状态、token存储、提供认证相关方法

## API调用示例

### 使用useApiRequest Hook（推荐）
```typescript
import { useApiRequest } from '../utils/api';

const MyComponent = () => {
    const api = useApiRequest();
    
    const fetchData = async () => {
        try {
            const data = await api.get('/user/info');
            console.log('用户信息:', data);
        } catch (error) {
            console.error('获取用户信息失败:', error);
        }
    };
    
    return (
        <button onClick={fetchData}>
            获取用户信息
        </button>
    );
};
```

### 直接使用API函数
```typescript
import { apiGet } from '../utils/api';
import { useAuth } from '../hooks';

const MyComponent = () => {
    const { token } = useAuth();
    
    const fetchData = async () => {
        if (token) {
            try {
                const data = await apiGet('/user/info', token);
                console.log('用户信息:', data);
            } catch (error) {
                console.error('获取用户信息失败:', error);
            }
        }
    };
    
    return (
        <button onClick={fetchData}>
            获取用户信息
        </button>
    );
};
```

## 路由配置

系统会自动处理登录状态检查：
- 未登录用户访问任何页面都会重定向到 `/login`
- 登录和回调页面不需要认证
- 登录成功后会重定向到主页

## 跨域处理

### 开发环境
使用代理配置（`src/setupProxy.js`）自动处理跨域问题：
- 所有 `/api` 请求会代理到 `REACT_APP_BACKEND_URL`
- 无需手动处理跨域

### 生产环境
生产环境需要后端配置CORS或使用Nginx反向代理。

## 部署说明

### 开发环境
```bash
npm start
```

### 生产构建
```bash
# 使用生产环境配置构建
npm run build
```

## 注意事项

1. **环境变量**：所有环境变量必须以 `REACT_APP_` 开头才能在前端使用
2. **代理配置**：开发环境的代理配置只在 `npm start` 时生效
3. **Token安全性**：Token存储在localStorage中，请确保在生产环境中采取适当的安全措施
4. **错误处理**：登录失败时会显示错误信息，并自动跳转回登录页面
5. **登出功能**：在侧边栏底部提供登出按钮，点击后会清除token并跳转到登录页面

## 故障排除

### 1. 登录跳转失败
- 检查环境变量配置是否正确
- 确认代理配置是否生效（开发环境）
- 确认后端登录接口是否正常工作

### 2. 404错误
- 检查代理配置中的 `/api` 路径是否正确配置
- 确认后端服务是否在指定地址运行
- 检查前端路由配置

### 3. 跨域问题
- 开发环境：检查 `setupProxy.js` 配置
- 生产环境：检查后端CORS配置或Nginx配置

### 4. 回调处理失败
- 检查URL中是否包含token或error参数
- 查看浏览器控制台是否有错误信息
- 确认回调URL配置是否正确

### 5. API调用失败
- 确认token是否正确保存
- 检查API请求头是否包含正确的Authorization字段
- 确认后端API是否支持Bearer token认证 