# TanStack Query + OpenAI SDK 集成指南

## 概述

这个项目已经集成了 TanStack Query 和 OpenAI SDK，并配置为使用自定义的 OpenAI API 服务器，支持**流式传输**功能。

## 设计系统

### 字体配置 🎨

项目已配置使用苹果平方字体（PingFang）作为主要字体：

#### 字体层级
- **主字体**: PingFang SC（简体中文）
- **繁体中文**: PingFang TC / PingFang HK
- **备用字体**: Hiragino Sans GB, Microsoft YaHei
- **代码字体**: SF Mono, Monaco, Fira Code

#### Tailwind 字体类
```tsx
// 默认字体（已自动应用 PingFang）
<div className="font-sans">默认文本</div>

// 明确使用 PingFang
<div className="font-pingfang">平方字体文本</div>

// 字体粗细
<div className="font-light">细体</div>
<div className="font-normal">常规</div>
<div className="font-medium">中等</div>
<div className="font-semibold">半粗</div>
<div className="font-bold">粗体</div>
```

#### CSS 字体配置
```css
/* 全局字体设置 */
body {
    font-family: 'PingFang SC', 'PingFang TC', 'PingFang HK', 
        'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 
        'Helvetica Neue', 'Arial', sans-serif;
}

/* 代码字体 */
code {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 
        'Fira Mono', 'Roboto Mono', 'Source Code Pro', Menlo, 
        Consolas, 'Courier New', monospace;
}
```

## API 配置

### 当前配置
- **API 地址**: `http://192.168.120.26:8080/v1`
- **集成页面**: ChatInterface.tsx
- **模型**: `deepseek-chat`
- **流式传输**: ✅ 已启用
- **认证**: 支持 API Key（可选）

### 环境配置

如果您的 API 服务器需要认证，请创建 `.env` 文件：

```env
REACT_APP_OPENAI_API_KEY=your_api_key_here
```

如果不需要认证，系统会使用默认的 `dummy-key`。

## 流式传输功能 🚀

### 核心特性
- ✅ **实时流式响应** - AI 回复逐字显示，如同真人打字
- ✅ **打字机效果** - 流畅的文字逐渐显示动画
- ✅ **光标动画** - 流式传输时显示闪烁光标
- ✅ **状态指示器** - "正在输入..." 状态提示
- ✅ **自动滚动** - 流式内容自动滚动到视图
- ✅ **头像动画** - AI 头像在流式传输时有脉冲效果

### 视觉效果
- 流式传输中的消息会显示：
  - 闪烁的紫色光标 `|`
  - AI 头像脉冲动画
  - "正在输入..." 状态指示器
  - 三个跳动的小圆点动画

## ChatInterface 集成

ChatInterface 已经完全集成了流式 OpenAI API，具有以下功能：

### 核心功能
- ✅ **流式 AI 对话** - 实时逐字显示 AI 回复
- ✅ 对话历史管理
- ✅ 错误处理和用户友好提示
- ✅ 加载状态显示
- ✅ 系统提示词配置

### 系统提示词
默认配置为锦湖日丽公司的专业 AI 助手：
```
你是锦湖日丽公司的AI助手，专门为用户提供专业、友好的服务。请用中文回答问题，保持专业和礼貌的语调。
```

## API 测试工具

### 连接测试
```tsx
import { testApiConnection, checkApiStatus } from '../utils/apiTest';

// 测试 API 连接
const testConnection = async () => {
  const result = await testApiConnection();
  console.log(result.success ? '连接成功' : '连接失败', result.message);
};

// 检查服务器状态
const checkStatus = async () => {
  const status = await checkApiStatus();
  console.log(status.available ? '服务器可用' : '服务器不可用', status.message);
};
```

## 使用方法

### 流式传输 Hooks

#### useStreamChatCompletion
用于流式 GPT 聊天完成：

```tsx
import { useStreamChatCompletion } from '../hooks/useOpenAI';
import { createUserMessage } from '../utils/openaiHelpers';

const MyComponent = () => {
  const [content, setContent] = useState('');
  const streamChatMutation = useStreamChatCompletion();

  const handleStreamChat = async () => {
    setContent(''); // 清空内容
    
    await streamChatMutation.mutateAsync({
      messages: [createUserMessage('你好，世界！')],
      model: 'deepseek-chat',
      onStream: (chunk: string) => {
        // 实时接收流式数据
        setContent(prev => prev + chunk);
      },
      onComplete: (fullContent: string) => {
        // 流式传输完成
        console.log('完整内容:', fullContent);
      },
      onError: (error: Error) => {
        // 错误处理
        console.error('流式传输失败:', error);
      }
    });
  };

  return (
    <div>
      <button onClick={handleStreamChat} disabled={streamChatMutation.isPending}>
        {streamChatMutation.isPending ? '流式传输中...' : '开始流式对话'}
      </button>
      <div>{content}</div>
    </div>
  );
};
```

#### useChatCompletion（非流式）
用于传统的一次性响应：

```tsx
import { useChatCompletion } from '../hooks/useOpenAI';
import { createUserMessage } from '../utils/openaiHelpers';

const MyComponent = () => {
  const chatMutation = useChatCompletion();

  const handleChat = async () => {
    const result = await chatMutation.mutateAsync({
      messages: [createUserMessage('你好，世界！')],
      model: 'deepseek-chat',
    });
    console.log(result);
  };

  return (
    <button onClick={handleChat} disabled={chatMutation.isPending}>
      {chatMutation.isPending ? '处理中...' : '发送消息'}
    </button>
  );
};
```

#### useModels
获取可用的模型列表：

```tsx
import { useModels } from '../hooks/useOpenAI';

const ModelSelector = () => {
  const { data: models, isLoading } = useModels();

  if (isLoading) return <div>加载中...</div>;

  return (
    <select>
      {models?.map(model => (
        <option key={model.id} value={model.id}>
          {model.id}
        </option>
      ))}
    </select>
  );
};
```

#### useEmbeddings
生成文本嵌入：

```tsx
import { useEmbeddings } from '../hooks/useOpenAI';

const EmbeddingComponent = () => {
  const embeddingMutation = useEmbeddings();

  const generateEmbedding = async () => {
    const result = await embeddingMutation.mutateAsync('要嵌入的文本');
    console.log(result.data[0].embedding);
  };

  return (
    <button onClick={generateEmbedding}>
      生成嵌入
    </button>
  );
};
```

#### useImageGeneration
生成图像：

```tsx
import { useImageGeneration } from '../hooks/useOpenAI';

const ImageGenerator = () => {
  const imageMutation = useImageGeneration();

  const generateImage = async () => {
    const result = await imageMutation.mutateAsync(
      '一只可爱的小猫咪在花园里玩耍',
      { size: '1024x1024', n: 1 }
    );
    console.log(result.data[0].url);
  };

  return (
    <button onClick={generateImage}>
      生成图像
    </button>
  );
};
```

### 辅助工具函数

#### 消息创建
```tsx
import { 
  createUserMessage, 
  createSystemMessage, 
  createAssistantMessage 
} from '../utils/openaiHelpers';

const messages = [
  createSystemMessage('你是一个有用的助手'),
  createUserMessage('请帮我写一首诗'),
  createAssistantMessage('好的，我来为您写一首诗...')
];
```

#### 响应格式化
```tsx
import { formatOpenAIResponse } from '../utils/openaiHelpers';

const response = await chatMutation.mutateAsync({...});
const content = formatOpenAIResponse(response);
```

#### API Key 检查
```tsx
import { checkApiKey } from '../utils/openaiHelpers';

if (!checkApiKey()) {
  alert('请设置 OpenAI API Key');
}
```

## 配置修改

### 更改 API 地址
如需更改 API 服务器地址，请修改 `src/config/openai.ts`：

```tsx
export const openai = new OpenAI({
  baseURL: 'http://your-api-server:port/v1',
  apiKey: process.env.REACT_APP_OPENAI_API_KEY || 'dummy-key',
  dangerouslyAllowBrowser: true,
});
```

### 更改模型
在 hooks 或 ChatInterface 中修改模型：

```tsx
// 在 useStreamChatCompletion 中
model: 'your-model-name', // 替换为您的模型名称

// 在 ChatInterface.tsx 中
model: 'deepseek-chat', // 当前使用的模型
```

### 自定义系统提示词
在 `ChatInterface.tsx` 中修改系统提示词：

```tsx
const systemPrompt = createSystemMessage(
  '你的自定义系统提示词'
);
```

### 流式传输参数调整
```tsx
await streamChatMutation.mutateAsync({
  messages: [...],
  model: 'deepseek-chat',
  temperature: 0.7,        // 创造性程度 (0-1)
  max_tokens: 1000,        // 最大输出长度
  stream: true,            // 启用流式传输
  // ... 其他参数
});
```

## TanStack Query 配置

查询客户端已在 `src/config/queryClient.ts` 中配置，包含以下默认设置：

- 重试次数：3次
- 缓存时间：10分钟
- 数据新鲜度：5分钟
- 窗口聚焦时不自动重新获取

## 示例组件

您可以参考 `src/components/OpenAIExample.tsx` 来了解如何使用这些功能。

## 注意事项

1. **网络访问**: 确保能够访问 `http://192.168.120.26:8080`
2. **CORS 配置**: API 服务器需要允许浏览器跨域访问
3. **流式支持**: 确保 API 服务器支持 Server-Sent Events (SSE)
4. **模型兼容性**: 验证服务器支持 `deepseek-chat` 模型
5. **网络稳定性**: 流式传输对网络稳定性要求较高

## 故障排除

### 流式传输相关问题

1. **流式传输中断**
   - 检查网络连接稳定性
   - 验证 API 服务器支持流式传输
   - 查看浏览器控制台错误信息

2. **内容显示异常**
   - 确认 API 返回格式正确
   - 检查字符编码设置
   - 验证流式数据解析逻辑

3. **性能问题**
   - 调整 `max_tokens` 参数
   - 优化组件渲染频率
   - 检查内存使用情况

### 常见问题

1. **连接失败**
   - 检查 API 服务器是否运行
   - 验证网络连接
   - 确认端口是否正确

2. **认证错误**
   - 检查 API Key 配置
   - 验证服务器认证要求

3. **模型不支持**
   - 使用 `useModels` hook 检查可用模型
   - 修改请求中的模型名称

4. **CORS 错误**
   - 确保 API 服务器配置了正确的 CORS 头
   - 检查 `dangerouslyAllowBrowser` 设置

### 调试工具

使用浏览器开发者工具调试流式传输：

```javascript
// 在控制台中监控流式传输
console.log('流式传输开始');

// 监控网络请求
// Network 标签页 -> 查看 SSE 连接

// 监控错误
window.addEventListener('error', console.error);
```

## 高级功能

### 自定义查询键
```tsx
const { data } = useQuery({
  queryKey: ['custom-chat', userId, messageId],
  queryFn: () => fetchCustomData(userId, messageId),
});
```

### 查询无效化
```tsx
import { useQueryClient } from '@tanstack/react-query';

const queryClient = useQueryClient();

// 使缓存无效
queryClient.invalidateQueries({ queryKey: ['openai-models'] });
```

### 乐观更新
```tsx
const mutation = useMutation({
  mutationFn: updateData,
  onMutate: async (newData) => {
    // 乐观更新
    await queryClient.cancelQueries({ queryKey: ['data'] });
    const previousData = queryClient.getQueryData(['data']);
    queryClient.setQueryData(['data'], newData);
    return { previousData };
  },
  onError: (err, newData, context) => {
    // 回滚
    queryClient.setQueryData(['data'], context?.previousData);
  },
});
```

## 错误处理

### 流式传输错误处理

```tsx
await streamChatMutation.mutateAsync({
  // ... 其他参数
  onError: (error: Error) => {
    console.error('流式传输失败:', error);
    
    // 根据错误类型进行处理
    if (error.message.includes('network')) {
      // 网络错误处理
    } else if (error.message.includes('auth')) {
      // 认证错误处理
    }
  }
});
```

### ChatInterface 错误处理

ChatInterface 包含完善的错误处理：

1. **网络错误**: 显示友好的错误消息
2. **流式传输中断**: 自动显示已接收的内容
3. **API 错误**: 记录详细错误信息到控制台
4. **超时处理**: 自动重试机制
5. **用户提示**: 提供清晰的错误反馈

## 性能优化

### 流式传输优化
- **自动滚动**: 流式内容自动滚动到视图
- **内存管理**: 及时清理流式传输状态
- **渲染优化**: 使用 React.memo 和 useCallback 优化重渲染

### 最佳实践
```tsx
// 1. 合理设置 max_tokens 避免过长响应
max_tokens: 1000,

// 2. 适当的 temperature 设置
temperature: 0.7, // 平衡创造性和准确性

// 3. 错误边界处理
onError: (error) => {
  // 记录错误但不中断用户体验
  console.error(error);
  showUserFriendlyMessage();
}
``` 