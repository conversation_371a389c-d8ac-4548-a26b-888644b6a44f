/* App.css */
.App {
    text-align: center;
}

.App-logo {
    height: 40vmin;
    pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
    .App-logo {
        animation: App-logo-spin infinite 20s linear;
    }
}

.App-header {
    background-color: #282c34;
    padding: 20px;
    color: white;
}

.App-link {
    color: #61dafb;
}

@keyframes App-logo-spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* 自定义动画 */
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 下拉菜单滑入动画 */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.animate-slideInDown {
    animation: slideInDown 0.2s ease-out forwards;
}

.animate-fade-in {
    animation: fade-in 0.6s ease-out forwards;
    opacity: 0;
}

.animate-slide-in {
    animation: slide-in 0.5s ease-out forwards;
    opacity: 0;
}

/* 动画延迟类 */
.animation-delay-200 {
    animation-delay: 0.2s;
    animation-fill-mode: forwards;
    opacity: 0;
}

.animation-delay-300 {
    animation-delay: 0.3s;
    animation-fill-mode: forwards;
    opacity: 0;
}

.animation-delay-400 {
    animation-delay: 0.4s;
    animation-fill-mode: forwards;
    opacity: 0;
}

.animation-delay-600 {
    animation-delay: 0.6s;
    animation-fill-mode: forwards;
    opacity: 0;
}

.animation-delay-800 {
    animation-delay: 0.8s;
    animation-fill-mode: forwards;
    opacity: 0;
}

.animation-delay-1200 {
    animation-delay: 1.2s;
    animation-fill-mode: forwards;
    opacity: 0;
}

/* 滑入上升动画 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slideInUp {
    animation: slideInUp 0.6s ease-out forwards;
}

/* 路由切换动画 */
.route-transition-group {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.route-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
}

/* 基础路由进入/退出动画 */
.route-enter {
    opacity: 0;
    transform: translateX(20px);
}

.route-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1), transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.route-exit {
    opacity: 1;
    transform: translateX(0);
}

.route-exit-active {
    opacity: 0;
    transform: translateX(-20px);
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1), transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
}

/* 优化的淡入淡出效果 */
.route-fade-enter {
    opacity: 0;
    transform: translateY(8px);
}

.route-fade-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1;
}

.route-fade-exit {
    opacity: 1;
    transform: translateY(0);
}

.route-fade-exit-active {
    opacity: 0;
    transform: translateY(-8px);
    transition: opacity 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 0;
}

/* 向左滑动效果 */
.route-slide-left-enter {
    opacity: 0;
    transform: translateX(100%);
}

.route-slide-left-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1), transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.route-slide-left-exit {
    opacity: 1;
    transform: translateX(0);
}

.route-slide-left-exit-active {
    opacity: 0;
    transform: translateX(-100%);
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1), transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
}

/* 向右滑动效果 */
.route-slide-right-enter {
    opacity: 0;
    transform: translateX(-100%);
}

.route-slide-right-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1), transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.route-slide-right-exit {
    opacity: 1;
    transform: translateX(0);
}

.route-slide-right-exit-active {
    opacity: 0;
    transform: translateX(100%);
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1), transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
}

/* 垂直滑动效果 */
.route-slide-enter {
    transform: translateY(20px);
    opacity: 0;
}

.route-slide-enter-active {
    transform: translateY(0);
    opacity: 1;
    transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.route-slide-exit {
    transform: translateY(0);
    opacity: 1;
}

.route-slide-exit-active {
    transform: translateY(-20px);
    opacity: 0;
    transition: transform 400ms cubic-bezier(0.4, 0, 0.2, 1), opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
}

/* 缩放效果 */
.route-scale-enter {
    opacity: 0;
    transform: scale(0.9);
}

.route-scale-enter-active {
    opacity: 1;
    transform: scale(1);
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1), transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.route-scale-exit {
    opacity: 1;
    transform: scale(1);
}

.route-scale-exit-active {
    opacity: 0;
    transform: scale(1.1);
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1), transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
}

/* 旋转效果 */
.route-rotate-enter {
    opacity: 0;
    transform: rotateY(90deg);
}

.route-rotate-enter-active {
    opacity: 1;
    transform: rotateY(0deg);
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1), transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.route-rotate-exit {
    opacity: 1;
    transform: rotateY(0deg);
}

.route-rotate-exit-active {
    opacity: 0;
    transform: rotateY(-90deg);
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1), transform 400ms cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
}

/* 背景渐变动画 - 浅色主题 */
@keyframes lightGradientShift {
    0% {
        background: linear-gradient(-45deg, #f0f9ff, #e0e7ff, #fdf2f8, #f0fdf4);
    }

    25% {
        background: linear-gradient(-45deg, #e0e7ff, #fdf2f8, #f0fdf4, #fef3c7);
    }

    50% {
        background: linear-gradient(-45deg, #fdf2f8, #f0fdf4, #fef3c7, #fee2e2);
    }

    75% {
        background: linear-gradient(-45deg, #f0fdf4, #fef3c7, #fee2e2, #f0f9ff);
    }

    100% {
        background: linear-gradient(-45deg, #fef3c7, #fee2e2, #f0f9ff, #e0e7ff);
    }
}

/* 背景渐变动画 - 深色主题 */
@keyframes darkGradientShift {
    0% {
        background: linear-gradient(-45deg, #0f172a, #1e1b4b, #581c87, #164e63);
    }

    25% {
        background: linear-gradient(-45deg, #1e1b4b, #581c87, #164e63, #365314);
    }

    50% {
        background: linear-gradient(-45deg, #581c87, #164e63, #365314, #7c2d12);
    }

    75% {
        background: linear-gradient(-45deg, #164e63, #365314, #7c2d12, #0f172a);
    }

    100% {
        background: linear-gradient(-45deg, #365314, #7c2d12, #0f172a, #1e1b4b);
    }
}

/* 应用背景渐变动画 */
.bg-gradient-animated-light {
    background: linear-gradient(-45deg, #f0f9ff, #e0e7ff, #fdf2f8, #f0fdf4);
    background-size: 400% 400%;
    animation: lightGradientShift 15s ease-in-out infinite;
}

.bg-gradient-animated-dark {
    background: linear-gradient(-45deg, #0f172a, #1e1b4b, #581c87, #164e63);
    background-size: 400% 400%;
    animation: darkGradientShift 15s ease-in-out infinite;
}

/* 文本截断样式 */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 底部滑入动画 */
@keyframes slideInBottom {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slideInBottom {
    animation: slideInBottom 0.6s ease-out forwards;
}

/* 渐变文本动画 */
@keyframes gradientText {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

.animate-gradient-text {
    background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #ec4899, #10b981);
    background-size: 400% 400%;
    animation: gradientText 3s ease infinite;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* 浮动效果 */
@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* 脉冲光效 */
@keyframes pulse-glow {

    0%,
    100% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }

    50% {
        box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
    }
}

.animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
}

/* 无动画效果 */
.route-none-enter,
.route-none-enter-active,
.route-none-exit,
.route-none-exit-active {
    opacity: 1;
    transform: none;
    transition: none;
}

/* 删除重复的动画定义以提高性能 */