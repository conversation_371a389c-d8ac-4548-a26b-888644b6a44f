import type { ChatMessage } from '../config/openai';

// 创建聊天消息的辅助函数
export const createChatMessage = (role: 'system' | 'user' | 'assistant', content: string): ChatMessage => {
    return { role, content };
};

// 创建系统消息
export const createSystemMessage = (content: string): ChatMessage => {
    return createChatMessage('system', content);
};

// 创建用户消息
export const createUserMessage = (content: string): ChatMessage => {
    return createChatMessage('user', content);
};

// 创建助手消息
export const createAssistantMessage = (content: string): ChatMessage => {
    return createChatMessage('assistant', content);
};

// 格式化OpenAI响应
export const formatOpenAIResponse = (response: any) => {
    if (response?.choices?.[0]?.message?.content) {
        return response.choices[0].message.content;
    }
    return '';
};

// 计算token数量的近似值（简单估算）
export const estimateTokens = (text: string): number => {
    // 简单的token估算：大约4个字符=1个token
    return Math.ceil(text.length / 4);
};

// 检查API Key是否存在
export const checkApiKey = (): boolean => {
    const apiKey = process.env.REACT_APP_OPENAI_API_KEY;
    return !!(apiKey && apiKey.length > 0 && apiKey !== 'your_openai_api_key_here');
};
