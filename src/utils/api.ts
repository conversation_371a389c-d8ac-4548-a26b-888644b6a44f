import axios, { AxiosRequestConfig, InternalAxiosRequestConfig } from 'axios';
import { toast } from 'react-hot-toast';
import useAuth from '../hooks/useAuth';
import { API_BASE_URL, STORAGE_KEYS } from './constants';
import {
    AppCategoriesResponse,
    AppDetailResponse,
    PaginatedResponse,
    FileDetail,
    DocumentType,
    VectorRecallResult,
    AppApiResponse,
    AiGeneratedContent,
} from '../types';

// =================================================================================
// TYPE DEFINITIONS
// =================================================================================

/**
 * 创建聊天会话请求参数类型
 */
export interface CreateSessionRequest {
    sessionTitle?: string;
    modelUsed?: string;
    metadata?: string;
    userId?: number;
}

/**
 * 更新聊天会话请求参数类型
 */
export interface UpdateSessionRequest {
    sessionTitle?: string;
    title?: string;
    modelUsed?: string;
    metadata?: string;
    status?: string;
}

/**
 * 提供商信息类型
 */
export interface ProviderData {
    id: number;
    providerCode: string;
    providerName: string;
    baseUrl: string;
    status: number;
    createdTime: string;
    updatedTime: string;
}

/**
 * 目标模型数据类型
 */
export interface TargetModelData {
    id: number;
    modelCode: string;
    modelName: string;
    modelType: 'CHAT' | 'IMAGE';
    providerId: number;
    contextWindow: number;
    maxOutputTokens: number;
    supportsStreaming: boolean;
    status: number;
    createdTime: string;
    updatedTime: string;
    provider: ProviderData;
}

/**
 * 模型数据类型
 */
export interface ModelData {
    id: number;
    requestModel: string;
    targetModelId: number;
    priority: number;
    status: number;
    createdTime: string;
    updatedTime: string;
    targetModel: TargetModelData;
    // 保留旧字段以保持向后兼容，从targetModel映射
    object?: string;
    created?: number;
    ownedBy?: string;
}

/**
 * 获取模型列表响应类型
 */
export interface GetModelsResponse {
    object: 'list';
    data: ModelData[];
}

/**
 * 会话数据类型
 */
export interface SessionData {
    id: string;
    userId: number;
    sessionTitle: string;
    modelUsed: string;
    status: string;
    metadata: string;
    createdAt: string;
    updatedAt: string;
}

/**
 * 创建会话响应类型
 */
export interface CreateSessionResponse {
    code: number;
    message: string;
    data: SessionData;
    timestamp: number;
}

/**
 * 获取会话列表响应类型
 */
export interface GetSessionsResponse {
    code: number;
    message: string;
    data: SessionData[];
    timestamp: number;
}

/**
 * 聊天消息数据类型
 */
export interface ChatMessage {
    id: number;
    sessionId: string;
    sender: 'user' | 'assistant' | 'system';
    content: string;
    contentType: 'text' | 'image_url';
    tokenCount: number;
    createdAt: string;
}

/**
 * 获取会话消息列表响应类型
 */
export interface GetSessionMessagesResponse {
    code: number;
    message: string;
    data: ChatMessage[];
    timestamp: number;
}

// =================================================================================
// IMAGE GENERATION TYPES
// =================================================================================

export interface ImageGenerationRequest {
    model: string;
    prompt: string;
    n?: number;
    size?: '256x256' | '512x512' | '1024x1024' | '1792x1024' | '1024x1792';
    quality?: 'standard' | 'hd';
    style?: 'natural' | 'vivid';
    user?: string;
    aspectRatio?: '1:1' | '16:9' | '9:16' | '4:3' | '3:4';
}

export interface ImageGenerationData {
    url: string;
    b64_json: string | null;
    revised_prompt?: string;
}

export interface ImageGenerationResponse {
    created: number;
    data: ImageGenerationData[];
}

// =================================================================================
// PRIVATE HELPERS
// =================================================================================

/**
 * 创建带有认证 token 的请求头
 * @param token - 认证 token
 */
const getAuthHeaders = (token?: string): HeadersInit => {
    const headers: HeadersInit = {
        'Content-Type': 'application/json',
    };

    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
};

// =================================================================================
// GENERIC API FUNCTIONS
// =================================================================================

/**
 * 通用的API请求函数
 * @param endpoint - API 接口路径
 * @param options - fetch 请求选项
 * @param token - 认证 token
 */
export const apiRequest = async <T>(
    endpoint: string,
    options: RequestInit = {},
    token?: string
): Promise<T> => {
    const url = `${API_BASE_URL}${endpoint}`;

    const response = await fetch(url, {
        ...options,
        headers: {
            ...getAuthHeaders(token),
            ...(options.headers || {}),
        },
    });

    const data = await response.json();

    // 检查响应内容中的业务状态码
    if (data && data.code === 401) {
        localStorage.removeItem(STORAGE_KEYS.DINGTALK_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.USER_INFO);
        window.location.reload();
        throw new Error(data.message || 'Token已过期，请重新登录');
    }

    // 检查业务错误状态码 500
    if (data && data.code === 500) {
        // 对于图片生成接口，显示 toast 错误提示（在 Library.tsx 中会被捕获并进一步处理）
        if (endpoint.includes('/images/generations')) {
            // 让 Library.tsx 的错误处理来显示更具体的错误信息
            throw new Error(data.message || '图片生成失败');
        } else {
            // 其他接口显示通用错误提示
            toast.error(data.message || '系统异常，请稍后再试');
            throw new Error(data.message || 'Server error with code 500');
        }
    }

    if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
};

/**
 * 发起 GET 请求
 */
export const apiGet = async <T>(endpoint: string, token?: string): Promise<T> => {
    return apiRequest<T>(endpoint, { method: 'GET' }, token);
};

/**
 * 发起 POST 请求
 */
export const apiPost = async <T>(
    endpoint: string,
    data?: any,
    token?: string
): Promise<T> => {
    return apiRequest<T>(
        endpoint,
        {
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined,
        },
        token
    );
};

/**
 * 发起 PUT 请求
 */
export const apiPut = async <T>(
    endpoint: string,
    data?: any,
    token?: string
): Promise<T> => {
    return apiRequest<T>(
        endpoint,
        {
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined,
        },
        token
    );
};

/**
 * 发起 DELETE 请求
 */
export const apiDelete = async <T>(endpoint: string, token?: string): Promise<T> => {
    return apiRequest<T>(endpoint, { method: 'DELETE' }, token);
};

/**
 * 生成图片
 * @param request - 图片生成请求参数
 * @param token - 认证 token
 */
export const generateImage = async (
    request: ImageGenerationRequest,
    token?: string
): Promise<ImageGenerationResponse> => {
    return apiPost<ImageGenerationResponse>('/v1/v1/images/generations', request, token);
};

/**
 * 查询我生成的图片
 * @param page - 页码
 * @param size - 每页数量
 * @param token - 认证 token
 */
export const getMyGeneratedImages = async (
    page: number,
    size: number,
    token?: string
) => {
    return apiGet<AppApiResponse<PaginatedResponse<AiGeneratedContent>>>(
        `/v1/api/vision/generations?page=${page}&size=${size}`,
        token
    );
};

/**
 * 查询公开的图片
 * @param page - 页码
 * @param size - 每页数量
 * @param token - 认证 token
 */
export const getPublicGeneratedImages = async (
    page: number,
    size: number,
    token?: string
) => {
    return apiGet<AppApiResponse<PaginatedResponse<AiGeneratedContent>>>(
        `/v1/api/vision/public-generations?page=${page}&size=${size}`,
        token
    );
};

/**
 * 发起 POST 请求，用于文件上传
 * @param endpoint - API 接口路径
 * @param formData - FormData 对象
 * @param token - 认证 token
 */
export const apiPostForm = async <T>(
    endpoint: string,
    formData: FormData,
    token?: string
): Promise<T> => {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers: HeadersInit = {};
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(url, {
        method: 'POST',
        body: formData,
        headers: headers,
    });

    const data = await response.json();

    if (data && data.code === 401) {
        localStorage.removeItem(STORAGE_KEYS.DINGTALK_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.USER_INFO);
        window.location.reload();
        throw new Error(data.message || 'Token已过期，请重新登录');
    }

    if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
};

// =================================================================================
// API ENDPOINT COLLECTIONS
// =================================================================================

/**
 * 应用中心相关API
 */
export const appsApi = {
    /**
     * 获取所有应用分类及应用列表
     */
    getAppCategories: (token?: string): Promise<AppCategoriesResponse> => {
        return apiGet<AppCategoriesResponse>('/v1/api/apps/categories', token);
    },

    /**
     * 根据ID获取应用详情
     * @param appId - 应用ID
     * @param token - 认证 token
     */
    getAppById: (appId: number, token?: string): Promise<AppDetailResponse> => {
        return apiGet<AppDetailResponse>(`/v1/api/apps/${appId}`, token);
    }
};

/**
 * 模型相关API
 */
export const modelsApi = {
    getModels: (token?: string): Promise<GetModelsResponse> =>
        apiGet<GetModelsResponse>('/v1/v1/models', token),
};

/**
 * 聊天会话相关API
 */
export const chatSessionApi = {
    /**
     * 创建新的聊天会话
     */
    createSession: (params: CreateSessionRequest, token?: string): Promise<CreateSessionResponse> => {
        const requestData: CreateSessionRequest = {
            sessionTitle: params.sessionTitle || '新建对话',
            modelUsed: params.modelUsed || 'deepseek-ai/DeepSeek-V3',
            metadata: params.metadata || JSON.stringify({
                settings: { temperature: 0.2, maxTokens: 128000 }
            }),
        };
        return apiPost<CreateSessionResponse>('/v1/api/chat/sessions', requestData, token);
    },

    /**
     * 获取用户的会话列表
     */
    getSessions: (token?: string): Promise<GetSessionsResponse> => {
        return apiGet<GetSessionsResponse>('/v1/api/chat/sessions', token);
    },

    /**
     * 删除会话
     */
    deleteSession: (sessionId: string, token?: string) =>
        apiDelete(`/v1/api/chat/sessions/${sessionId}`, token),

    /**
     * 获取指定会话的历史消息
     */
    getSessionMessages: (sessionId: string, token?: string): Promise<GetSessionMessagesResponse> => {
        return apiGet<GetSessionMessagesResponse>(`/v1/api/chat/sessions/${sessionId}/messages`, token);
    },

    /**
     * 更新会话信息
     */
    updateSession: (sessionId: string, data: UpdateSessionRequest, token?: string) =>
        apiPut(`/v1/api/chat/sessions/${sessionId}`, data, token),
};

// =================================================================================
// KNOWLEDGE BASE API
// =================================================================================

interface ListFilesParams {
    type?: DocumentType;
    originalFileName?: string;
    page?: number;
    size?: number;
    // sort?: string; // Add if sorting is needed
}

/**
 * 获取知识库文件列表
 */
export const listFiles = async (
    params: ListFilesParams,
    token?: string
): Promise<PaginatedResponse<FileDetail>> => {
    const queryParams = new URLSearchParams();
    if (params.type) {
        queryParams.append('type', params.type);
    }
    if (params.originalFileName) {
        queryParams.append('originalFileName', params.originalFileName);
    }
    if (params.page !== undefined) {
        queryParams.append('page', params.page.toString());
    }
    if (params.size !== undefined) {
        queryParams.append('size', params.size.toString());
    }

    const endpoint = `/v1/api/ai/file/list?${queryParams.toString()}`;
    const response = await apiGet<any>(endpoint, token);
    return response.data;
};

interface RecallParams {
    fileId: number;
    query: string;
}

/**
 * 向量召回
 */
export const recallFileContent = async (
    params: RecallParams,
    token?: string
): Promise<VectorRecallResult[]> => {
    const queryParams = new URLSearchParams();
    queryParams.append('fileId', params.fileId.toString());
    queryParams.append('query', params.query);

    const endpoint = `/v1/api/ai/file/recall?${queryParams.toString()}`;
    const response = await apiGet<{ data: VectorRecallResult[] }>(endpoint, token);
    return response.data;
};

interface UploadFilesParams {
    type: DocumentType;
    files: File[];
}

/**
 * 上传文件到知识库
 */
export const uploadFiles = async (
    params: UploadFilesParams,
    token?: string
): Promise<any> => {
    const formData = new FormData();
    formData.append('type', params.type);
    for (const file of params.files) {
        formData.append('files', file);
    }

    const endpoint = `/v1/api/ai/file/upload`;
    return apiPostForm<any>(endpoint, formData, token);
};

/**
 * 删除知识库文档
 */
export const deleteDocument = async (
    documentId: string,
    token?: string
): Promise<any> => {
    const endpoint = `/v1/api/ai/file/${documentId}`;
    return apiDelete<any>(endpoint, token);
};

/**
 * 批量删除生成的图片
 * @param imageIds - 要删除的图片ID数组
 * @param token - 认证token
 */
export const deleteGeneratedImages = async (
    imageIds: string[],
    token?: string
): Promise<any> => {
    try {
        const response = await fetch(`${API_BASE_URL}/v1/api/vision/generations`, {
            method: 'DELETE',
            headers: getAuthHeaders(token),
            body: JSON.stringify(imageIds),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error("Failed to delete generated images:", error);
        throw error;
    }
};

// =================================================================================
// API HOOKS
// =================================================================================

/**
 * 提供带 token 认证的 API 请求方法的 Hook
 */
export const useApiRequest = () => {
    const { token } = useAuth();

    return {
        get: <T>(endpoint: string) => apiGet<T>(endpoint, token || undefined),
        post: <T>(endpoint: string, data?: any) => apiPost<T>(endpoint, data, token || undefined),
        put: <T>(endpoint: string, data?: any) => apiPut<T>(endpoint, data, token || undefined),
        delete: <T>(endpoint: string) => apiDelete<T>(endpoint, token || undefined),
    };
};

const V1_API_BASE_URL = '/api/v1';

// 创建 Axios 实例
const api = axios.create({
    baseURL: V1_API_BASE_URL,
    timeout: 300000,
});

// 请求拦截器
api.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        // 在发送请求之前做些什么
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 响应拦截器
api.interceptors.response.use(
    (response) => {
        // 如果响应数据中存在 code: 500，则认为是业务错误
        if (response.data && response.data.code === 500) {
            toast.error(response.data.message || '系统异常，请稍后再试');
            return Promise.reject(new Error(response.data.message || 'Server error with code 500'));
        }
        // 对于成功的响应，直接返回 data 部分
        return response.data;
    },
    (error) => {
        // 处理网络错误等
        if (error.response) {
            // 服务器返回了错误状态码
            const { status, data } = error.response;
            if (status === 401) {
                // 处理未授权，例如跳转到登录页
                // window.location.href = '/login';
                console.error('Unauthorized, redirecting to login...');
            } else {
                toast.error(data.message || `请求错误，状态码：${status}`);
            }
        } else if (error.request) {
            // 请求已发出，但没有收到响应
            toast.error('网络错误，请检查您的网络连接');
        } else {
            // 其他错误
            toast.error('发生未知错误');
        }

        return Promise.reject(error);
    }
);


export default api;