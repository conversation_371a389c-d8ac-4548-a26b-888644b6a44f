// API 相关常量
export const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';

// 钉钉登录相关配置
export const DINGTALK_CONFIG = {
    CLIENT_ID: process.env.REACT_APP_DINGTALK_CLIENT_ID || 'dingb3ymimqlndb1zoqp',
    CLIENT_SECRET: process.env.REACT_APP_DINGTALK_CLIENT_SECRET || 'p_uY7uC8YoJGZAdCszW2mCzIMivh2rv5yEyccBnNPfzFteoBgYQ9LlBQc34Bi6dT',
    AUTH_API: process.env.REACT_APP_DINGTALK_AUTH_API || '/api/ddlogin/auth',
    // CALLBACK_URL: process.env.REACT_APP_DINGTALK_CALLBACK_URL || `http://ai.kumhosunny.cn/v1/api/ddlogin/auth`,
    CALLBACK_URL: process.env.REACT_APP_DINGTALK_CALLBACK_URL || `http://192.168.120.26:3000/v1/api/ddlogin/auth`,
} as const;

// 本地存储键名
export const STORAGE_KEYS = {
    USER: 'user',
    TOKEN: 'token',
    DINGTALK_TOKEN: 'dingtalk_token',
    THEME: 'theme',
    LANGUAGE: 'language',
    USER_INFO: 'user_info',
} as const;

// 路由路径
export const ROUTES = {
    HOME: '/',
    ABOUT: '/about',
    CONTACT: '/contact',
    LOGIN: '/login',
    REGISTER: '/register',
    DASHBOARD: '/dashboard',
    DINGTALK_CALLBACK: '/dingtalk-callback',
} as const;

// 主题配置
export const THEMES = {
    LIGHT: 'light',
    DARK: 'dark',
} as const;

// 分页配置
export const PAGINATION = {
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 100,
} as const;

// 验证规则
export const VALIDATION = {
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PASSWORD_MIN_LENGTH: 8,
    USERNAME_MIN_LENGTH: 3,
} as const; 