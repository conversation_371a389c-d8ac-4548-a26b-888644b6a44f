// 通用类型定义
export interface User {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    createdAt: Date;
    updatedAt: Date;
}

// 钉钉用户信息类型定义
export interface DingTalkUserInfo {
    employeeId: number;
    unionId: string;
    loginName: string;
    actualName: string;
    avatar: string;
    gender: number;
    phone: string;
    email: string;
    departmentId: number;
    administratorFlag: boolean;
    disabledFlag: boolean;
    deletedFlag: boolean;
    remark: string;
    updateTime: string;
    createTime: string;
}

// 用户信息API响应类型
export interface UserInfoResponse {
    code: number;
    message: string;
    data: DingTalkUserInfo;
    timestamp: number;
}

export interface ApiResponse<T> {
    data: T;
    message: string;
    success: boolean;
}

export interface PaginationParams {
    page: number;
    limit: number;
    total?: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: PaginationParams;
}

// 主题相关类型
export type Theme = 'light' | 'dark';

// 状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// 表单验证类型
export interface ValidationError {
    field: string;
    message: string;
}

// =================================================================================
// 应用中心（AIApps）相关类型
// =================================================================================

export interface App {
    id: number;
    name: string;
    description: string;
    avatar?: string;
    categoryId: number;
}

export interface AppCategory {
    id: number;
    name: string;
    description: string;
    sortOrder: number;
    apps: App[];
}

export interface AppSettings {
    id: number;
    appId: number;
    model: string;
    systemPrompt: string;
    temperature: number;
}

export interface AppTool {
    id: number;
    appId: number;
    toolName: string;
    toolType: 'api' | 'function' | 'workflow';
    config: string; // JSON string
}

export interface AppDetail extends App {
    settings: AppSettings;
    tools: AppTool[];
}

// API 响应类型 for AI Apps
export interface AppApiResponse<T> {
    code: number;
    message: string;
    data: T;
    timestamp: number;
}

export type AppCategoriesResponse = AppApiResponse<AppCategory[]>;
export type AppDetailResponse = AppApiResponse<AppDetail>;

// =================================================================================
// 知识库相关类型
// =================================================================================

// 文档类型
export type DocumentType = 'personal' | 'department' | 'company';

// 文档状态
export type EmbeddingStatus = 'pending' | 'done' | 'failed';

// 权限类型
export type PermissionType = 'read' | 'write' | 'admin';

// 文档基础信息
export interface Document {
    id: number;
    fileId?: number;
    title: string;
    content?: string;
    contentHash?: string;
    type: DocumentType;
    path?: string;
    ownerId: number;
    departmentId?: number | null;
    isPublic: boolean;
    createdAt: string;
    updatedAt: string;
}

// 文档片段信息
export interface DocumentChunk {
    id: number;
    documentId: number;
    chunkIndex: number;
    content: string;
    vectorId?: string;
    embeddingStatus: EmbeddingStatus;
    embeddingUpdatedAt?: string;
    createdAt: string;
}

// 文档权限信息
export interface DocumentPermission {
    id: number;
    documentId: number;
    userId?: number | null;
    departmentId?: number | null;
    permission: PermissionType;
    createdAt: string;
}

// 知识库统计信息
export interface KnowledgeBaseStats {
    total: number;
    personal: number;
    department: number;
    company: number;
}

// 筛选条件
export interface DocumentFilter {
    type?: DocumentType;
    search?: string;
    fileType?: string;
}

// 向量回测结果
export interface VectorTestResult {
    similarity: number;
    chunkContent: string;
    chunkIndex: number;
}

// 知识库API响应类型
export interface KnowledgeApiResponse<T> {
    code: number;
    message: string;
    data: T;
    timestamp: number;
}

export type DocumentListResponse = KnowledgeApiResponse<{
    documents: Document[];
    total: number;
}>;

export type DocumentDetailResponse = KnowledgeApiResponse<{
    document: Document;
    chunks: DocumentChunk[];
    permissions: DocumentPermission[];
}>;

export type VectorTestResponse = KnowledgeApiResponse<VectorTestResult[]>;

export interface UploadedFile {
    fileId: string;
    filename: string;
    path: string;
}

export interface UploadedFileDetail {
    id: number;
    fileName: string;
    originalFileName: string;
    bucketName: string;
    fileType: string;
    fileSize: string;
    filePath: string;
    path: string;
    uploadTime: string;
    userId: string;
    vectorChunks: string;
    textContent: string;
}

export interface FileDetail {
    fileId: number;
    userId: number;
    originalFileName: string;
    fileUrl: string;
    path: string; // 完整的文件访问路径URL
    fileType: string;
    fileSize: string;
    uploadTime: string;
    documentId: number;
    documentTitle: string;
    documentType: DocumentType;
    documentCreatedAt: string;
}

export interface Pageable {
    sort: {
        empty: boolean;
        sorted: boolean;
        unsorted: boolean;
    };
    offset: number;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    unpaged: boolean;
}

export interface PaginatedResponse<T> {
    content: T[];
    pageable: Pageable;
    last: boolean;
    totalPages: number;
    totalElements: number;
    first: boolean;
    size: number;
    number: number;
    sort: {
        empty: boolean;
        sorted: boolean;
        unsorted: boolean;
    };
    numberOfElements: number;
    empty: boolean;
}

export interface VectorRecallResult {
    score: number;
    content: string;
    chunkIndex: number;
    vectorId: string;
}

export interface AiGeneratedContent {
    id: number;
    contentId: string;
    userId: number;
    contentType: 'IMAGE';
    generationStatus: 'COMPLETED' | 'PENDING' | 'FAILED';
    prompt: string;
    modelUsed: string;
    size: string;
    ossUrl: string;
    isPublic: boolean;
    isFeatured: boolean;
    viewCount: number;
    likeCount: number;
    createdAt: string;
    updatedAt: string;
} 