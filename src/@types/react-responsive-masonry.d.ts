declare module 'react-responsive-masonry' {
    import React from 'react';

    export interface ResponsiveMasonryProps {
        columnsCountBreakPoints?: Record<number, number>;
        gutterBreakpoints?: Record<number, string>;
        style?: React.CSSProperties;
        children: React.ReactNode;
    }

    export interface MasonryProps {
        columnsCount?: number;
        gutter?: string;
        className?: string;
        style?: React.CSSProperties;
        containerTag?: string;
        itemTag?: string;
        itemStyle?: React.CSSProperties;
        sequential?: boolean;
        children: React.ReactNode;
    }

    export const ResponsiveMasonry: React.FC<ResponsiveMasonryProps>;
    export const Masonry: React.FC<MasonryProps>;

    export default Masonry;
} 