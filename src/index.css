@tailwind base;
@tailwind components;
@tailwind utilities;

/* 优化字体加载策略 */
@layer base {
    html {
        font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'PingFang TC',
            'Hiragino Sans GB', 'Microsoft YaHei', 'Source Han Sans SC', 'Noto Sans CJK SC',
            'WenQuanYi Micro Hei', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
    }
}

body {
    margin: 0;
    font-family: inherit;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'kern' 1;
    text-rendering: optimizeLegibility;
    /* 确保字体快速渲染 */
    font-display: swap;
}

code {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Fira Mono',
        'Roboto Mono', 'Source Code Pro', Menlo, Consolas, 'Courier New', monospace;
}

/* 针对中文字体的优化 */
* {
    font-variant-ligatures: none;
    -webkit-font-feature-settings: 'kern' 1;
    font-feature-settings: 'kern' 1;
}

/* 强制使用系统字体的类 */
.font-system {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
        'Helvetica Neue', 'Arial', sans-serif !important;
}

/* 强制使用中文字体的类 */
.font-chinese {
    font-family: 'PingFang SC', 'PingFang TC', 'Hiragino Sans GB',
        'Microsoft YaHei', 'Source Han Sans SC', 'Noto Sans CJK SC',
        'WenQuanYi Micro Hei', sans-serif !important;
}

/* 自定义滚动条样式 */
.scrollbar-light {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.scrollbar-light::-webkit-scrollbar {
    width: 8px;
}

.scrollbar-light::-webkit-scrollbar-track {
    background: transparent;
}

.scrollbar-light::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.scrollbar-light::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.8);
}

.scrollbar-dark {
    scrollbar-width: thin;
    scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

.scrollbar-dark::-webkit-scrollbar {
    width: 8px;
}

.scrollbar-dark::-webkit-scrollbar-track {
    background: transparent;
}

.scrollbar-dark::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.5);
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.scrollbar-dark::-webkit-scrollbar-thumb:hover {
    background-color: rgba(75, 85, 99, 0.8);
}

/* 文本截断样式 */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

/* 自定义 transform 样式 */
.scale-102 {
    transform: scale(1.02);
}

.scale-98 {
    transform: scale(0.98);
}

/* Agent 卡片动画增强 */
@keyframes subtle-bounce {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-2px);
    }
}

.agent-card-hover {
    animation: subtle-bounce 0.6s ease-in-out;
}

/* 右到左循环滚动动画 */
@keyframes scroll-rtl {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-50%);
    }
}

.animate-scroll-rtl {
    animation: scroll-rtl 20s linear infinite;
}

/* 鼠标悬停时暂停滚动 */
.animate-scroll-rtl:hover {
    animation-play-state: paused;
}

/* 自定义滑块样式 */
.slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    outline: none;
    border: none;
}

.slider-thumb::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slider-thumb::-webkit-slider-thumb:hover {
    background: #2563eb;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transform: scale(1.1);
}

.slider-thumb::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border: none;
    transition: all 0.2s ease;
}

.slider-thumb::-moz-range-thumb:hover {
    background: #2563eb;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transform: scale(1.1);
}

.slider-thumb::-webkit-slider-track {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: transparent;
}

.slider-thumb::-moz-range-track {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: transparent;
}

/* 深色模式滑块 */
.dark .slider-thumb::-webkit-slider-thumb {
    background: #60a5fa;
}

.dark .slider-thumb::-webkit-slider-thumb:hover {
    background: #3b82f6;
}

.dark .slider-thumb::-moz-range-thumb {
    background: #60a5fa;
}

.dark .slider-thumb::-moz-range-thumb:hover {
    background: #3b82f6;
}