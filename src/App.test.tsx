import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from './App';

test('renders main title in welcome page', () => {
    render(<App />);
    const mainTitle = screen.getByText(/您好，我是/i);
    expect(mainTitle).toBeInTheDocument();
});

test('renders menu toggle button', () => {
    render(<App />);
    const menuButton = screen.getByTitle(/显示菜单|隐藏菜单/i);
    expect(menuButton).toBeInTheDocument();
});

test('renders new conversation button when sidebar is visible', () => {
    render(<App />);
    const newConversationButton = screen.getByText(/新建对话/i);
    expect(newConversationButton).toBeInTheDocument();
});

test('renders sidebar with intelligence platform text', () => {
    render(<App />);
    const platformTexts = screen.getAllByText(/锦湖日丽 AI/i);
    expect(platformTexts[0]).toBeInTheDocument();
});

test('renders apps button in sidebar', () => {
    render(<App />);
    const appsButton = screen.getByText(/应用中心/i);
    expect(appsButton).toBeInTheDocument();
}); 