import { useState, useCallback } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { chatSessionApi, CreateSessionRequest, CreateSessionResponse, SessionData } from '../utils/api';
import useAuth from './useAuth';

interface SessionState {
    currentSessionId: string | null;
    isCreatingSession: boolean;
}

const useSession = () => {
    const { token, isAuthenticated } = useAuth();
    const [sessionState, setSessionState] = useState<SessionState>({
        currentSessionId: null,
        isCreatingSession: false,
    });

    // 获取会话列表的 query
    const sessionsQuery = useQuery({
        queryKey: ['sessions', token],
        queryFn: () => {
            if (!isAuthenticated || !token) {
                throw new Error('用户未认证，请重新登录');
            }
            return chatSessionApi.getSessions(token);
        },
        enabled: isAuthenticated && !!token, // 只有在认证状态下才执行查询
        refetchOnWindowFocus: false,
        staleTime: 1000 * 60 * 5, // 5分钟内不重新获取
        retry: (failureCount, error) => {
            // 如果是认证错误，不进行重试
            if (error.message.includes('登录') || error.message.includes('认证') || error.message.includes('Token已过期')) {
                return false;
            }
            return failureCount < 2;
        },
    });

    // 创建会话的mutation
    const createSessionMutation = useMutation({
        mutationFn: (data: { params: CreateSessionRequest; token: string }) => {
            const { params, token } = data;
            if (!token) {
                throw new Error('用户未认证，请重新登录');
            }
            return chatSessionApi.createSession(params, token);
        },
        onSuccess: (response: CreateSessionResponse) => {
            // 从API响应的data字段中获取会话ID
            const sessionId = response.data.id.toString();
            setSessionState(prev => ({
                ...prev,
                currentSessionId: sessionId,
                isCreatingSession: false,
            }));
            console.log('会话创建成功:', sessionId, '完整响应:', response);

            // 创建会话成功后，重新获取会话列表
            sessionsQuery.refetch();
        },
        onError: (error: Error) => {
            setSessionState(prev => ({
                ...prev,
                isCreatingSession: false,
            }));
            console.error('会话创建失败:', error);
        },
    });

    // 删除会话的 mutation
    const deleteSessionMutation = useMutation({
        mutationFn: (data: { sessionId: string; token: string }) => {
            const { sessionId, token } = data;
            if (!token) {
                throw new Error('用户未认证，请重新登录');
            }
            return chatSessionApi.deleteSession(sessionId, token);
        },
        onSuccess: () => {
            // 删除成功后，重新获取会话列表
            sessionsQuery.refetch();
        },
        onError: (error: Error) => {
            console.error('删除会话失败:', error);
            // 这里可以添加一些用户提示，例如使用 react-toastify
        },
    });

    // 创建新会话
    const createNewSession = useCallback(async (params?: Partial<CreateSessionRequest>) => {
        // 检查认证状态
        if (!isAuthenticated || !token) {
            throw new Error('用户未认证，请重新登录');
        }

        setSessionState(prev => ({ ...prev, isCreatingSession: true }));

        const defaultParams: CreateSessionRequest = {
            sessionTitle: '新建对话',
            modelUsed: 'deepseek-ai/DeepSeek-V3',
            metadata: JSON.stringify({
                settings: { temperature: 0.2, maxTokens: 128000 }
            }),
        };

        try {
            const finalParams = { ...defaultParams, ...params };
            const response = await createSessionMutation.mutateAsync({
                params: finalParams,
                token: token,
            });

            // 返回会话ID而不是整个响应
            return {
                sessionId: response.data.id.toString(),
                sessionData: response.data
            };
        } catch (error) {
            throw error;
        }
    }, [createSessionMutation, isAuthenticated, token]);

    // 删除会话
    const deleteSession = useCallback((sessionId: string) => {
        if (!isAuthenticated || !token) {
            throw new Error('用户未认证，请重新登录');
        }
        return deleteSessionMutation.mutateAsync({ sessionId, token });
    }, [deleteSessionMutation, isAuthenticated, token]);

    // 重置会话状态
    const resetSession = useCallback(() => {
        setSessionState({
            currentSessionId: null,
            isCreatingSession: false,
        });
    }, []);

    // 设置当前会话ID（用于从URL参数中设置）
    const setCurrentSessionId = useCallback((sessionId: string | null) => {
        setSessionState(prev => ({
            ...prev,
            currentSessionId: sessionId,
        }));
    }, []);

    // 刷新会话列表
    const refreshSessions = useCallback(() => {
        sessionsQuery.refetch();
    }, [sessionsQuery]);

    return {
        ...sessionState,
        createNewSession,
        resetSession,
        setCurrentSessionId,
        refreshSessions,
        deleteSession,
        // 会话列表相关
        sessions: sessionsQuery.data?.data || [],
        isLoadingSessions: sessionsQuery.isLoading,
        sessionsError: sessionsQuery.error,
        // 创建会话相关
        isLoading: createSessionMutation.isPending || sessionState.isCreatingSession,
        error: createSessionMutation.error,
    };
};

export default useSession; 