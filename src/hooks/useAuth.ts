import { useState, useEffect, useRef } from 'react';
import useLocalStorage from './useLocalStorage';
import { STORAGE_KEYS } from '../utils/constants';
import type { DingTalkUserInfo, UserInfoResponse } from '../types';

interface AuthState {
    token: string | null;
    isAuthenticated: boolean;
    userInfo: DingTalkUserInfo | null;
}

const useAuth = () => {
    const [token, setToken] = useLocalStorage<string | null>(STORAGE_KEYS.DINGTALK_TOKEN, null);
    const [userInfo, setUserInfo] = useLocalStorage<DingTalkUserInfo | null>(STORAGE_KEYS.USER_INFO, null);
    const [authState, setAuthState] = useState<AuthState>({
        token,
        isAuthenticated: !!token,
        userInfo
    });

    // 添加请求防重标记
    const isFetchingUserInfo = useRef(false);

    useEffect(() => {
        setAuthState({
            token,
            isAuthenticated: !!token,
            userInfo
        });
    }, [token, userInfo]);

    // 在应用启动时，如果有token但没有用户信息，则尝试获取用户信息
    useEffect(() => {
        const fetchUserInfoOnStartup = async () => {
            // 防重检查：如果已经在请求中，或者没有token，或者已有用户信息，则不执行
            if (isFetchingUserInfo.current || !token || userInfo) {
                return;
            }

            isFetchingUserInfo.current = true;
            console.log('开始获取用户信息...');

            try {
                const headers: Record<string, string> = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                };

                const response = await fetch('/v1/api/user/info', {
                    method: 'GET',
                    headers,
                });

                if (response.ok) {
                    const result: UserInfoResponse = await response.json();
                    if (result.code === 200) {
                        setUserInfo(result.data);
                        console.log('启动时获取用户信息成功:', result.data.actualName);
                    } else {
                        console.warn('获取用户信息失败，响应码:', result.code, result.message);
                    }
                } else {
                    console.warn('获取用户信息失败，HTTP状态码:', response.status);
                }
            } catch (error) {
                console.warn('启动时获取用户信息失败:', error);
            } finally {
                isFetchingUserInfo.current = false;
            }
        };

        fetchUserInfoOnStartup();
    }, [token, userInfo]); // 移除 setUserInfo 依赖项，避免无限循环

    const login = (newToken: string) => {
        setToken(newToken);
        console.log('用户登录成功，token已保存');
    };

    const setUser = (user: DingTalkUserInfo) => {
        setUserInfo(user);
        console.log('用户信息已保存:', user.actualName);
    };

    const logout = () => {
        setToken(null);
        setUserInfo(null);
        console.log('用户已退出登录');
    };

    const getAuthHeaders = () => {
        if (!token) {
            return {};
        }
        return {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    };

    return {
        ...authState,
        login,
        logout,
        setUser,
        getAuthHeaders
    };
};

export default useAuth; 