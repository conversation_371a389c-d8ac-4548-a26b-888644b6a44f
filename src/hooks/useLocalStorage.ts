import { useState, useCallback, useEffect } from 'react';

// 创建一个自定义事件，用于在同一个窗口内同步状态
const dispatchStorageEvent = (key: string, newValue: string | null) => {
    window.dispatchEvent(new CustomEvent('local-storage', { detail: { key, newValue } }));
};

function useLocalStorage<T>(key: string, initialValue: T) {
    // 获取初始值
    const [storedValue, setStoredValue] = useState<T>(() => {
        try {
            const item = window.localStorage.getItem(key);
            return item ? JSON.parse(item) : initialValue;
        } catch (error) {
            console.error(`Error reading localStorage key "${key}":`, error);
            return initialValue;
        }
    });

    // 使用 useCallback 确保 setValue 函数引用稳定
    const setValue = useCallback((value: T | ((val: T) => T)) => {
        try {
            // 允许 value 是一个函数，这样我们就有了和 useState 相同的 API
            const valueToStore = value instanceof Function ? value(storedValue) : value;
            setStoredValue(valueToStore);
            const newValueString = JSON.stringify(valueToStore);
            window.localStorage.setItem(key, newValueString);
            dispatchStorageEvent(key, newValueString); // 分发自定义事件
        } catch (error) {
            console.error(`Error setting localStorage key "${key}":`, error);
        }
    }, [key, storedValue]);

    // 监听自定义的 local-storage 事件
    useEffect(() => {
        const handleStorageChange = (event: Event) => {
            const customEvent = event as CustomEvent;
            if (customEvent.detail.key === key) {
                try {
                    setStoredValue(customEvent.detail.newValue ? JSON.parse(customEvent.detail.newValue) : initialValue);
                } catch (error) {
                    console.error(`Error parsing localStorage key "${key}" on custom event:`, error);
                    setStoredValue(initialValue);
                }
            }
        };

        window.addEventListener('local-storage', handleStorageChange);

        return () => {
            window.removeEventListener('local-storage', handleStorageChange);
        };
    }, [key, initialValue]);

    return [storedValue, setValue] as const;
}

export default useLocalStorage; 