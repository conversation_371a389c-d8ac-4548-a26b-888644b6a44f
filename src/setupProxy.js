const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function (app) {
    const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://192.168.120.26:3333';

    // 代理 /v1 路径到后端 (原有的 OpenAI 代理)
    app.use(
        '/v1',
        createProxyMiddleware({
            target: backendUrl,
            changeOrigin: true,
        })
    );

    // 代理 /api 路径到后端 (用于业务API)
    app.use(
        '/api',
        createProxyMiddleware({
            target: backendUrl,
            changeOrigin: true,
            pathRewrite: {
                '^/api': '/api', // 保持 /api 前缀
            },
        })
    );
}; 