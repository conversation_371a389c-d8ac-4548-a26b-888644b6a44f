import React, { useState, useEffect } from 'react';
import Masonry, { ResponsiveMasonry } from "react-responsive-masonry";

interface MediaItem {
    id: string;
    type: 'image' | 'video';
    url: string;
    thumbnail?: string;
    aspectRatio: number;
}

interface VisualModelTopProps {
    isDarkMode: boolean;
    // onToggleTheme: () => void;
}

// 模拟数据
const generateMockData = (): MediaItem[] => {
    const mockItems: MediaItem[] = [
        {
            id: '1',
            type: 'image',
            url: 'https://picsum.photos/seed/forest/400/600',
            aspectRatio: 0.67,
        },
        {
            id: '2',
            type: 'video',
            url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            thumbnail: 'https://picsum.photos/seed/city/400/300',
            aspectRatio: 1.33,
        },
        {
            id: '3',
            type: 'image',
            url: 'https://picsum.photos/seed/abstract/400/800',
            aspectRatio: 0.5,
        },
        {
            id: '4',
            type: 'image',
            url: 'https://picsum.photos/seed/ocean/400/500',
            aspectRatio: 0.8,
        },
        {
            id: '5',
            type: 'video',
            url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            thumbnail: 'https://picsum.photos/seed/space/400/400',
            aspectRatio: 1,
        },
        {
            id: '6',
            type: 'image',
            url: 'https://picsum.photos/seed/mountain/400/350',
            aspectRatio: 1.14,
        },
        {
            id: '7',
            type: 'image',
            url: 'https://picsum.photos/seed/future/400/700',
            aspectRatio: 0.57,
        },
        {
            id: '8',
            type: 'video',
            url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            thumbnail: 'https://picsum.photos/seed/animals/400/450',
            aspectRatio: 0.89,
        },
        {
            id: '9',
            type: 'image',
            url: 'https://picsum.photos/seed/vintage/400/550',
            aspectRatio: 0.73,
        },
        {
            id: '10',
            type: 'image',
            url: 'https://picsum.photos/seed/pattern/400/320',
            aspectRatio: 1.25,
        },
        {
            id: '11',
            type: 'video',
            url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            thumbnail: 'https://picsum.photos/seed/aurora/400/600',
            aspectRatio: 0.67,
        },
        {
            id: '12',
            type: 'image',
            url: 'https://picsum.photos/seed/cyberpunk/400/750',
            aspectRatio: 0.53,
        },
        {
            id: '13',
            type: 'image',
            url: 'https://picsum.photos/seed/nature/400/480',
            aspectRatio: 0.83,
        },
        {
            id: '14',
            type: 'video',
            url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            thumbnail: 'https://picsum.photos/seed/tech/400/380',
            aspectRatio: 1.05,
        },
        {
            id: '15',
            type: 'image',
            url: 'https://picsum.photos/seed/art/400/620',
            aspectRatio: 0.65,
        },
        {
            id: '16',
            type: 'image',
            url: 'https://picsum.photos/seed/minimal/400/340',
            aspectRatio: 1.18,
        }
    ];

    return mockItems;
};

const VisualModelTop: React.FC<VisualModelTopProps> = ({ isDarkMode }) => {
    const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
    const [loading, setLoading] = useState(true);

    const themeClasses = {
        background: isDarkMode ? 'bg-black' : 'bg-white',
    };

    useEffect(() => {
        // 模拟数据加载
        const timer = setTimeout(() => {
            setMediaItems(generateMockData());
            setLoading(false);
        }, 300);

        return () => clearTimeout(timer);
    }, []);

    return (
        <div className={`relative min-h-screen ${themeClasses.background} p-4`}>
            {/* 内容容器，应用模糊效果 */}
            <div className="filter blur-md">
                {/* 加载状态 */}
                {loading && (
                    <div className="flex justify-center items-center h-screen">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500"></div>
                    </div>
                )}

                {/* 使用 react-responsive-masonry 实现瀑布流 */}
                {!loading && (
                    <ResponsiveMasonry
                        columnsCountBreakPoints={{
                            350: 1,
                            640: 2,
                            768: 3,
                            1024: 4
                        }}
                        gutterBreakpoints={{
                            350: "0.5rem",
                            640: "1rem",
                            768: "1.5rem"
                        }}
                    >
                        <Masonry>
                            {mediaItems.map((item) => (
                                <div
                                    key={item.id}
                                    className="relative overflow-hidden rounded-lg group cursor-pointer transition-transform duration-200 hover:scale-[1.02]"
                                >
                                    {/* 媒体内容 */}
                                    {item.type === 'image' ? (
                                        <img
                                            src={item.url}
                                            alt=""
                                            className="w-full h-auto object-cover"
                                            loading="lazy"
                                        />
                                    ) : (
                                        <video
                                            src={item.url}
                                            poster={item.thumbnail}
                                            autoPlay
                                            loop
                                            muted
                                            playsInline
                                            className="w-full h-auto object-cover"
                                        />
                                    )}
                                </div>
                            ))}
                        </Masonry>
                    </ResponsiveMasonry>
                )}
            </div>

            {/* 居中提示 */}
            <div className="absolute inset-0 flex justify-center items-center">
                <div className="bg-gray-900 bg-opacity-75 text-white py-4 px-8 rounded-lg text-2xl font-semibold">
                    功能未开放
                </div>
            </div>
        </div>
    );
};

export default VisualModelTop; 