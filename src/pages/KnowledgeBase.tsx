import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { DocumentType, FileDetail, Pageable, VectorRecallResult } from '../types';
import { listFiles, recallFileContent, uploadFiles, deleteDocument } from '../utils/api';
import useAuth from '../hooks/useAuth';
import AttachmentViewer from '../components/attachment/AttachmentViewer';
import { AttachmentContent } from '../components/attachment/common/types';
import { toast } from 'react-hot-toast';

interface KnowledgeBaseProps {
    isDarkMode: boolean;
    // onToggleTheme: () => void;
}

interface FileTypeSelectorProps {
    value: string;
    onChange: (value: string) => void;
    isDarkMode: boolean;
}

const FileTypeSelector: React.FC<FileTypeSelectorProps> = React.memo(({ value, onChange, isDarkMode }) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    const fileTypes = useMemo(() => [
        { value: '', label: '全部类型' },
        { value: 'pdf', label: 'PDF 文档' },
        { value: 'doc', label: 'Word 文档' },
        { value: 'xls', label: 'Excel 表格' },
        { value: 'ppt', label: 'PowerPoint' },
        { value: 'txt', label: '文本文件' },
        { value: 'md', label: 'Markdown' },
    ], []);

    const selectedType = useMemo(() =>
        fileTypes.find(type => type.value === value) || fileTypes[0],
        [fileTypes, value]
    );

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const themeClasses = useMemo(() => ({
        bg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        text: isDarkMode ? 'text-gray-200' : 'text-gray-800',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-500',
        border: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        hoverBg: isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100',
        selectedBg: isDarkMode ? 'bg-blue-600' : 'bg-blue-500',
    }), [isDarkMode]);

    const handleItemClick = useCallback((typeValue: string) => {
        onChange(typeValue);
        setIsOpen(false);
    }, [onChange]);

    return (
        <div className="relative min-w-[160px]" ref={dropdownRef}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className={`w-full flex items-center justify-between px-4 py-2 text-left transition-colors duration-200 ${themeClasses.bg} border ${themeClasses.border} rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 hover:border-blue-400`}
            >
                <span className={`${themeClasses.text} text-sm`}>{selectedType.label}</span>
                <svg className={`w-4 h-4 ml-2 flex-shrink-0 ${themeClasses.textSecondary} transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
            </button>

            {isOpen && (
                <div className={`absolute z-20 w-full mt-2 ${themeClasses.bg} border ${themeClasses.border} rounded-lg shadow-xl overflow-hidden`}>
                    <ul className="py-1">
                        {fileTypes.map(type => (
                            <li
                                key={type.value}
                                onClick={() => handleItemClick(type.value)}
                                className={`px-4 py-2 cursor-pointer transition-colors duration-150 ${value === type.value ? `${themeClasses.selectedBg} text-white font-medium` : `${themeClasses.text} ${themeClasses.hoverBg}`}`}
                            >
                                <span className="text-sm">{type.label}</span>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
});

FileTypeSelector.displayName = 'FileTypeSelector';

const KnowledgeBase: React.FC<KnowledgeBaseProps> = React.memo(({ isDarkMode }) => {
    const [activeTab, setActiveTab] = useState<DocumentType>('personal');
    const [searchTerm, setSearchTerm] = useState('');
    const [fileTypeFilter, setFileTypeFilter] = useState('');
    const [files, setFiles] = useState<FileDetail[]>([]);
    const [pageable, setPageable] = useState<Pageable | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const [selectedDocument, setSelectedDocument] = useState<FileDetail | null>(null);
    const [isDrawerOpen, setIsDrawerOpen] = useState(false);
    const [activeViewTab, setActiveViewTab] = useState<'detail' | 'vector'>('detail');
    const [vectorTestResults, setVectorTestResults] = useState<VectorRecallResult[]>([]);
    const [vectorTestQuery, setVectorTestQuery] = useState('');
    const [isLoadingVectorTest, setIsLoadingVectorTest] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [deleteConfirmModal, setDeleteConfirmModal] = useState<{
        isOpen: boolean;
        file: FileDetail | null;
    }>({
        isOpen: false,
        file: null,
    });
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [currentPage, setCurrentPage] = useState(0);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const scrollContainerRef = useRef<HTMLDivElement>(null);

    const { token } = useAuth();

    const fetchFiles = useCallback(async (page = 0, size = 10, append = false, isPolling = false) => {
        if (!token) return;

        if (append) {
            setIsLoadingMore(true);
        } else if (!isPolling) {
            // 只有在非轮询时才显示加载状态和清空文件列表
            setLoading(true);
            setFiles([]);
            setCurrentPage(0);
            setHasMore(true);
        }
        setError(null);

        try {
            const params = {
                type: activeTab,
                originalFileName: searchTerm,
                page,
                size,
            };
            const response = await listFiles(params, token);

            if (append) {
                setFiles(prev => [...prev, ...response.content]);
            } else {
                setFiles(response.content);
            }

            setPageable(response.pageable);
            setCurrentPage(page);

            // 检查是否还有更多数据
            const totalPages = Math.ceil(response.totalElements / size);
            setHasMore(page + 1 < totalPages);

            // 检查是否有未完成的文件（排除失败状态和未知状态），如果有则3秒后重新查询
            const hasProcessingFiles = response.content.some((file: FileDetail) =>
                file.status !== 'COMPLETED' && file.status !== 'FAILED' &&
                ['UPLOADING', 'UPLOADED', 'PARSING', 'PARSED', 'VECTORIZING'].includes(file.status)
            );

            if (hasProcessingFiles && !append) {
                setTimeout(() => {
                    fetchFiles(page, size, append, true); // 传递 isPolling = true
                }, 3000);
            }

        } catch (err) {
            setError(err instanceof Error ? err.message : '获取文件失败');
            console.error(err);
        } finally {
            if (!isPolling) {
                // 只有在非轮询时才清除加载状态
                setLoading(false);
            }
            setIsLoadingMore(false);
        }
    }, [activeTab, searchTerm, token]);

    useEffect(() => {
        fetchFiles();
    }, [fetchFiles]);

    // 滚动监听，实现自动加载更多
    useEffect(() => {
        const handleScroll = () => {
            if (!scrollContainerRef.current || isLoadingMore || !hasMore) return;

            const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
            // 当滚动到距离底部100px时开始加载更多
            if (scrollHeight - scrollTop - clientHeight < 100) {
                fetchFiles(currentPage + 1, 10, true);
            }
        };

        const scrollContainer = scrollContainerRef.current;
        if (scrollContainer) {
            scrollContainer.addEventListener('scroll', handleScroll);
            return () => scrollContainer.removeEventListener('scroll', handleScroll);
        }
    }, [currentPage, isLoadingMore, hasMore, fetchFiles]);

    const themeClasses = useMemo(() => ({
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-gray-100' : 'text-gray-900',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-600',
        textMuted: isDarkMode ? 'text-gray-500' : 'text-gray-500',
        cardBg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        cardBorder: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        inputBg: isDarkMode ? 'bg-gray-800' : 'bg-gray-50',
        border: isDarkMode ? 'border-gray-700' : 'border-gray-300',
        modalBg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        hoverBg: isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50',
        divider: isDarkMode ? 'border-gray-700' : 'border-gray-200'
    }), [isDarkMode]);

    // 获取文件扩展名
    const getFileExtension = (path?: string) => {
        if (!path) return '';
        return path.split('.').pop()?.toUpperCase() || '';
    };

    // 获取文档类型标签
    const getTypeLabel = (type: DocumentType) => {
        switch (type) {
            case 'personal': return '个人';
            case 'department': return '部门';
            case 'company': return '公司';
        }
    };

    // 获取文档类型颜色
    const getTypeColor = (type: DocumentType) => {
        switch (type) {
            case 'personal': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
            case 'department': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            case 'company': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
        }
    };

    // 格式化时间
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', { hour12: false });
    };

    // 获取文件状态显示信息
    const getStatusInfo = (status: string) => {
        switch (status) {
            case 'UPLOADING':
                return { label: '上传中', color: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400' };
            case 'UPLOADED':
                return { label: '已上传', color: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400' };
            case 'PARSING':
                return { label: '解析中', color: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400' };
            case 'PARSED':
                return { label: '已解析', color: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400' };
            case 'VECTORIZING':
                return { label: '向量化中', color: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400' };
            case 'COMPLETED':
                return { label: '处理完成', color: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400' };
            case 'FAILED':
                return { label: '处理失败', color: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400' };
            default:
                return { label: '未知状态', color: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400' };
        }
    };

    const handleViewDocument = (document: FileDetail, tab: 'detail' | 'vector' = 'detail') => {
        setSelectedDocument(document);
        setActiveViewTab(tab);
        setIsDrawerOpen(true);
    };

    const performVectorTest = async () => {
        if (!selectedDocument || !vectorTestQuery.trim() || !token) return;
        setIsLoadingVectorTest(true);
        setVectorTestResults([]);

        try {
            const results = await recallFileContent(
                {
                    fileId: selectedDocument.fileId,
                    query: vectorTestQuery,
                },
                token
            );
            setVectorTestResults(results);
        } catch (err) {
            console.error('Vector recall failed:', err);
            // Optionally, set an error state to show in the UI
        } finally {
            setIsLoadingVectorTest(false);
        }
    };

    const extractTextFromApiContent = (content: string): string => {
        try {
            const regex = /"text":"(.*?)"/g;
            const matches = content.matchAll(regex);
            const texts = Array.from(matches, m => JSON.parse(`"${m[1]}"`));
            return texts.join('\n\n') || content;
        } catch {
            return content;
        }
    };

    const handleDeleteDocument = (file: FileDetail) => {
        setDeleteConfirmModal({
            isOpen: true,
            file: file,
        });
    };

    const confirmDelete = async () => {
        if (!deleteConfirmModal.file || !token) return;

        setIsDeleting(true);
        try {
            await deleteDocument(deleteConfirmModal.file.documentId.toString(), token);
            toast.success('删除成功');
            setDeleteConfirmModal({ isOpen: false, file: null });
            // 刷新文件列表
            await fetchFiles();
        } catch (error) {
            console.error('删除失败:', error);
            toast.error('删除失败，请稍后重试');
        } finally {
            setIsDeleting(false);
        }
    };

    const cancelDelete = () => {
        setDeleteConfirmModal({ isOpen: false, file: null });
    };

    const tabs: { key: DocumentType; label: string }[] = [
        { key: 'personal', label: '个人文档' },
        { key: 'department', label: '部门文档' },
        { key: 'company', label: '公司文档' },
    ];

    const handleUploadClick = () => {
        fileInputRef.current?.click();
    };

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFiles = event.target.files;
        if (!selectedFiles || selectedFiles.length === 0 || !token) {
            return;
        }

        setIsUploading(true);
        try {
            await uploadFiles(
                {
                    type: activeTab,
                    files: Array.from(selectedFiles),
                },
                token
            );
            // Refresh file list after successful upload
            await fetchFiles();
        } catch (err) {
            console.error('File upload failed:', err);
            // You can add user-facing error notification here
            setError('文件上传失败，请稍后重试。');
        } finally {
            setIsUploading(false);
            // Reset file input to allow uploading the same file again
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    // 将FileDetail转换为AttachmentContent
    const convertFileToAttachmentContent = (file: FileDetail): AttachmentContent => {
        const extension = file.originalFileName.split('.').pop()?.toLowerCase() || '';

        let type: AttachmentContent['type'] = 'unsupported';
        if (['pdf'].includes(extension)) {
            type = 'pdf';
        } else if (['doc', 'docx'].includes(extension)) {
            type = 'word';
        } else if (['xls', 'xlsx'].includes(extension)) {
            type = 'excel';
        } else if (['png', 'jpg', 'jpeg', 'gif', 'webp'].includes(extension)) {
            type = 'image';
        } else if (['txt', 'md', 'json', 'js', 'ts', 'tsx', 'jsx', 'py', 'java', 'cpp', 'c', 'h'].includes(extension)) {
            type = 'text';
        }

        return {
            type,
            title: file.originalFileName,
            url: file.path, // 使用path字段作为附件地址
        };
    };

    const renderFileList = () => {
        if (loading) {
            return (
                <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
                </div>
            );
        }

        if (error) {
            return (
                <div className="text-center py-10">
                    <p className="text-red-500">{error}</p>
                    <button
                        onClick={() => fetchFiles()}
                        className={`mt-4 px-4 py-2 rounded-md transition-colors ${isDarkMode ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-blue-500 hover:bg-blue-600 text-white'}`}
                    >
                        重试
                    </button>
                </div>
            );
        }

        if (files.length === 0) {
            return (
                <div className="text-center py-10">
                    <p className={`${themeClasses.textSecondary}`}>没有找到文件。</p>
                </div>
            );
        }

        return (
            <div className="space-y-1">
                {files.map((file) => (
                    <div
                        key={file.fileId}
                        className={`group rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-all duration-200 cursor-pointer`}
                        onClick={() => handleViewDocument(file)}
                    >
                        <div className="flex items-center py-4 px-6">
                            {/* 左侧文件图标 */}
                            <div className="flex-shrink-0 mr-3">
                                <div className={`w-8 h-8 rounded flex items-center justify-center ${getFileExtension(file.originalFileName) === 'PDF'
                                    ? 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
                                    : getFileExtension(file.originalFileName) === 'DOC' || getFileExtension(file.originalFileName) === 'DOCX'
                                        ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'
                                        : getFileExtension(file.originalFileName) === 'XLS' || getFileExtension(file.originalFileName) === 'XLSX'
                                            ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400'
                                            : getFileExtension(file.originalFileName) === 'PPT' || getFileExtension(file.originalFileName) === 'PPTX'
                                                ? 'bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400'
                                                : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
                                    }`}>
                                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </div>

                            {/* 主要内容区域 */}
                            <div className="flex-1 min-w-0">
                                {/* 文件名和大小 */}
                                <div className="flex items-center space-x-3 mb-1">
                                    <h3 className={`${themeClasses.text} font-medium text-sm truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors`}>
                                        {file.originalFileName}
                                    </h3>
                                    <span className={`${themeClasses.textMuted} text-xs flex-shrink-0`}>
                                        {(parseInt(file.fileSize) / (1024 * 1024)).toFixed(1)} MB
                                    </span>
                                </div>

                                {/* 文件状态和上传时间 */}
                                <div className="flex items-center space-x-4 text-xs mb-2">
                                    {/* 文件状态 */}
                                    <div className="flex items-center space-x-1">
                                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusInfo(file.status).color}`}>
                                            {getStatusInfo(file.status).label}
                                        </span>
                                    </div>

                                    {/* 上传时间 */}
                                    <div className="flex items-center space-x-1 text-gray-400 dark:text-gray-500">
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                        <span>{new Date(file.uploadTime).toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })}</span>
                                    </div>
                                </div>

                                {/* 错误信息显示（如果有） */}
                                {file.status === 'FAILED' && file.errorMessage && (
                                    <p className="text-xs text-red-600 dark:text-red-400 mb-2 line-clamp-2">
                                        错误: {file.errorMessage}
                                    </p>
                                )}
                            </div>

                            {/* 右侧标签和操作区域 */}
                            <div className="flex items-center space-x-6 ml-4">
                                {/* 标签区域 */}
                                <div className="flex flex-wrap gap-2">
                                    <span className={`px-2 py-0.5 text-xs font-medium rounded ${getTypeColor(file.documentType)}`}>
                                        {getTypeLabel(file.documentType)}
                                    </span>
                                    <span className="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 rounded">
                                        {getFileExtension(file.originalFileName)}
                                    </span>
                                </div>

                                {/* 操作按钮 */}
                                <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleViewDocument(file);
                                        }}
                                        className="p-1.5 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                                        title="查看"
                                    >
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                    </button>
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            window.open(file.path, '_blank');
                                        }}
                                        className="p-1.5 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors"
                                        title="下载"
                                    >
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                    </button>
                                    {activeTab === 'personal' && (
                                        <button
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleDeleteDocument(file);
                                            }}
                                            className="p-1.5 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
                                            title="删除"
                                        >
                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        );
    };

    return (
        <div className={`min-h-screen ${themeClasses.background} flex flex-col`}>


            <div className="flex-1 flex overflow-hidden">
                {/* 左侧文件夹导航 */}
                <div className="w-64 flex-shrink-0 p-6 pr-0">
                    <nav className="space-y-1">
                        {tabs.map((tab) => (
                            <button
                                key={tab.key}
                                onClick={() => setActiveTab(tab.key)}
                                className={`group relative w-full flex items-center transition-all duration-200 ${activeTab === tab.key
                                    ? 'z-10'
                                    : 'z-0'
                                    }`}
                            >
                                {/* 文件夹主体 */}
                                <div className={`flex items-center space-x-3 px-4 py-3 rounded-l-lg transition-all duration-200 ${activeTab === tab.key
                                    ? `${themeClasses.cardBg} ${themeClasses.text} shadow-lg translate-x-2`
                                    : `${themeClasses.textSecondary} hover:${themeClasses.text} hover:bg-gray-50 dark:hover:bg-gray-800/30 hover:translate-x-1`
                                    }`}
                                    style={{
                                        minWidth: '200px',
                                        clipPath: activeTab === tab.key
                                            ? 'polygon(0 0, calc(100% - 12px) 0, 100% 50%, calc(100% - 12px) 100%, 0 100%)'
                                            : 'polygon(0 0, calc(100% - 8px) 0, calc(100% - 4px) 50%, calc(100% - 8px) 100%, 0 100%)'
                                    }}
                                >
                                    {/* 文件夹图标 */}
                                    <div className={`p-1.5 rounded ${activeTab === tab.key
                                        ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'
                                        : 'bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500'
                                        }`}>
                                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                                        </svg>
                                    </div>
                                    <span className="font-medium text-sm">{tab.label}</span>

                                    {/* 活跃指示器 */}
                                    {activeTab === tab.key && (
                                        <div className="ml-auto w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                                    )}
                                </div>


                            </button>
                        ))}
                    </nav>
                </div>

                {/* 右侧内容区域 */}
                <div className="flex-1 flex flex-col overflow-hidden">
                    {/* 搜索和筛选栏 */}
                    <div className="flex items-center justify-between mb-6 flex-shrink-0 px-6 pt-6">
                        <div className="flex items-center space-x-4">
                            <FileTypeSelector value={fileTypeFilter} onChange={setFileTypeFilter} isDarkMode={isDarkMode} />
                            <div className="relative flex-grow max-w-xs">
                                <input
                                    type="text"
                                    placeholder="搜索文档标题或内容..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className={`w-full pl-10 pr-4 py-2 text-sm border ${themeClasses.border} ${themeClasses.inputBg} ${themeClasses.text} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors`}
                                />
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg className={`h-5 w-5 ${themeClasses.textMuted}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center space-x-4">
                            <input
                                type="file"
                                ref={fileInputRef}
                                onChange={handleFileChange}
                                multiple
                                className="hidden"
                                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.md,.json"
                            />
                            <button
                                onClick={handleUploadClick}
                                disabled={isUploading}
                                className={`px-4 py-2 flex items-center justify-center text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-400 disabled:cursor-not-allowed ${isDarkMode ? 'focus:ring-offset-gray-800' : ''}`}
                            >
                                {isUploading ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        上传中...
                                    </>
                                ) : (
                                    <>
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                        </svg>
                                        上传文件
                                    </>
                                )}
                            </button>
                        </div>
                    </div>

                    {/* 文档表格 */}
                    <div className="mt-6 flex-1 overflow-hidden px-6">
                        <div
                            ref={scrollContainerRef}
                            className="h-full overflow-y-auto"
                            style={{ maxHeight: 'calc(100vh - 280px)' }}
                        >
                            {renderFileList()}

                            {/* 加载更多指示器 */}
                            {isLoadingMore && (
                                <div className="flex justify-center items-center py-4">
                                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                                    <span className={`ml-2 text-sm ${themeClasses.textSecondary}`}>加载更多...</span>
                                </div>
                            )}

                            {/* 无更多数据提示 */}
                            {!hasMore && files.length > 0 && (
                                <div className="text-center py-4">
                                    <span className={`text-sm ${themeClasses.textMuted}`}>已显示全部内容</span>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* 文档详情抽屉 */}
            <>
                {/* 背景遮罩 */}
                <div
                    className={`fixed inset-0 bg-black transition-opacity duration-300 ${isDrawerOpen ? 'bg-opacity-50' : 'bg-opacity-0 pointer-events-none'} z-40`}
                    onClick={() => setIsDrawerOpen(false)}
                />

                {/* 抽屉内容 */}
                <div
                    className={`fixed top-0 right-0 h-full w-full max-w-7xl ${themeClasses.cardBg} shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${isDrawerOpen ? 'translate-x-0' : 'translate-x-full'}`}
                >
                    {selectedDocument && (
                        <div className="flex flex-col h-full">
                            {/* 抽屉头部 */}
                            <div className={`flex justify-between items-center px-6 py-4 border-b ${themeClasses.divider} bg-gradient-to-r ${isDarkMode ? 'from-gray-800 to-gray-700' : 'from-white to-gray-50'}`}>
                                <div className="flex items-center space-x-4">
                                    <div className="flex-shrink-0 h-12 w-12 flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                                        <span className="text-xs font-bold text-white">{getFileExtension(selectedDocument.originalFileName)}</span>
                                    </div>
                                    <div>
                                        <h2 className={`text-xl font-bold ${themeClasses.text} truncate max-w-md`}>
                                            {selectedDocument.originalFileName}
                                        </h2>
                                        <p className={`text-sm ${themeClasses.textSecondary}`}>
                                            {(parseInt(selectedDocument.fileSize) / 1024).toFixed(2)} KB • {formatDate(selectedDocument.uploadTime)}
                                        </p>
                                    </div>
                                </div>
                                <button
                                    onClick={() => setIsDrawerOpen(false)}
                                    className={`p-2 rounded-full ${themeClasses.hoverBg} transition-colors`}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            {/* 抽屉主体内容 */}
                            <div className="flex flex-1 overflow-hidden">
                                {/* 左侧 - 附件内容区域 */}
                                <div className={`flex-1 border-r ${themeClasses.divider} bg-gradient-to-b ${isDarkMode ? 'from-gray-900 to-gray-800' : 'from-gray-50 to-white'}`}>
                                    <AttachmentViewer
                                        isDarkMode={isDarkMode}
                                        onClose={() => { }} // 空函数，因为关闭功能由外部抽屉控制
                                        content={convertFileToAttachmentContent(selectedDocument)}
                                    />
                                </div>

                                {/* 右侧 - 信息和操作区域 */}
                                <div className={`w-96 flex flex-col ${themeClasses.cardBg}`}>
                                    {/* 右侧选项卡 */}
                                    <div className={`flex border-b ${themeClasses.divider} bg-gradient-to-r ${isDarkMode ? 'from-gray-800 to-gray-700' : 'from-gray-50 to-white'}`}>
                                        <button
                                            onClick={() => setActiveViewTab('detail')}
                                            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 font-medium text-sm transition-all duration-200 ${activeViewTab === 'detail'
                                                ? `border-b-2 border-blue-500 ${isDarkMode ? 'text-blue-400 bg-gray-700' : 'text-blue-600 bg-white'}`
                                                : `${themeClasses.textSecondary} hover:${isDarkMode ? 'text-blue-400 bg-gray-700' : 'text-blue-600 bg-gray-50'}`
                                                }`}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span>文档详情</span>
                                        </button>
                                        <button
                                            onClick={() => setActiveViewTab('vector')}
                                            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 font-medium text-sm transition-all duration-200 ${activeViewTab === 'vector'
                                                ? `border-b-2 border-blue-500 ${isDarkMode ? 'text-blue-400 bg-gray-700' : 'text-blue-600 bg-white'}`
                                                : `${themeClasses.textSecondary} hover:${isDarkMode ? 'text-blue-400 bg-gray-700' : 'text-blue-600 bg-gray-50'}`
                                                }`}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span>向量测试</span>
                                        </button>
                                    </div>

                                    {/* 右侧内容区域 */}
                                    <div className="flex-1 overflow-y-auto p-6">
                                        {activeViewTab === 'detail' && (
                                            <div className="space-y-6">
                                                <div className={`p-4 rounded-xl ${isDarkMode ? 'bg-gray-700 border border-gray-600' : 'bg-blue-50 border border-blue-200'}`}>
                                                    <h3 className={`text-lg font-semibold ${themeClasses.text} mb-4`}>基本信息</h3>
                                                    <dl className="space-y-3">
                                                        <div className="flex justify-between">
                                                            <dt className={`text-sm font-medium ${themeClasses.textSecondary}`}>文件ID:</dt>
                                                            <dd className={`text-sm ${themeClasses.text} font-mono bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded`}>{selectedDocument.fileId}</dd>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <dt className={`text-sm font-medium ${themeClasses.textSecondary}`}>文档类型:</dt>
                                                            <dd>
                                                                <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getTypeColor(selectedDocument.documentType)}`}>
                                                                    {getTypeLabel(selectedDocument.documentType)}
                                                                </span>
                                                            </dd>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <dt className={`text-sm font-medium ${themeClasses.textSecondary}`}>文件大小:</dt>
                                                            <dd className={`text-sm ${themeClasses.text}`}>{(parseInt(selectedDocument.fileSize) / 1024).toFixed(2)} KB</dd>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <dt className={`text-sm font-medium ${themeClasses.textSecondary}`}>上传时间:</dt>
                                                            <dd className={`text-sm ${themeClasses.text}`}>{formatDate(selectedDocument.uploadTime)}</dd>
                                                        </div>
                                                        <div className="flex justify-between">
                                                            <dt className={`text-sm font-medium ${themeClasses.textSecondary}`}>创建时间:</dt>
                                                            <dd className={`text-sm ${themeClasses.text}`}>{formatDate(selectedDocument.documentCreatedAt)}</dd>
                                                        </div>
                                                    </dl>
                                                </div>

                                                <div className={`p-4 rounded-xl ${isDarkMode ? 'bg-gray-700 border border-gray-600' : 'bg-green-50 border border-green-200'}`}>
                                                    <h3 className={`text-lg font-semibold ${themeClasses.text} mb-3`}>文件链接</h3>
                                                    <a
                                                        href={selectedDocument.path}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className={`inline-flex items-center space-x-2 text-blue-500 hover:text-blue-600 transition-colors`}
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                                        </svg>
                                                        <span className="text-sm truncate">查看原文件</span>
                                                    </a>
                                                </div>
                                            </div>
                                        )}
                                        {activeViewTab === 'vector' && (
                                            <div className="space-y-4">
                                                <div className={`p-4 rounded-xl ${isDarkMode ? 'bg-gray-700 border border-gray-600' : 'bg-purple-50 border border-purple-200'}`}>
                                                    <h3 className={`text-lg font-semibold ${themeClasses.text} mb-3`}>向量相似度测试</h3>
                                                    <p className={`text-sm ${themeClasses.textSecondary} mb-4`}>输入查询文本，测试与文档内容的向量相似度</p>

                                                    <div className="space-y-3">
                                                        <textarea
                                                            placeholder="输入测试查询文本..."
                                                            value={vectorTestQuery}
                                                            onChange={(e) => setVectorTestQuery(e.target.value)}
                                                            rows={3}
                                                            className={`w-full px-3 py-2 text-sm border ${themeClasses.border} ${themeClasses.inputBg} ${themeClasses.text} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none`}
                                                        />
                                                        <button
                                                            onClick={performVectorTest}
                                                            disabled={isLoadingVectorTest || !vectorTestQuery.trim()}
                                                            className={`w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200`}
                                                        >
                                                            {isLoadingVectorTest ? (
                                                                <>
                                                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                                    </svg>
                                                                    测试中...
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                                                    </svg>
                                                                    执行测试
                                                                </>
                                                            )}
                                                        </button>
                                                    </div>
                                                </div>

                                                {/* 测试结果 */}
                                                <div className="space-y-3">
                                                    {vectorTestResults.length > 0 ? (
                                                        <>
                                                            <h4 className={`text-md font-semibold ${themeClasses.text} flex items-center space-x-2`}>
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                </svg>
                                                                <span>测试结果 ({vectorTestResults.length} 个匹配)</span>
                                                            </h4>
                                                            {vectorTestResults.map((result) => (
                                                                <div key={result.vectorId} className={`p-4 rounded-lg border-l-4 ${result.score > 0.4
                                                                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                                                                    : result.score > 0.38
                                                                        ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
                                                                        : 'border-red-500 bg-red-50 dark:bg-red-900/20'
                                                                    }`}>
                                                                    <div className="flex justify-between items-start mb-2">
                                                                        <span className={`text-xs font-medium px-2 py-1 rounded-full ${isDarkMode ? 'bg-gray-600 text-gray-200' : 'bg-gray-200 text-gray-700'}`}>
                                                                            块 #{result.chunkIndex}
                                                                        </span>
                                                                        <div className="flex items-center space-x-2">
                                                                            <span className={`text-xs font-bold px-2 py-1 rounded-full ${result.score > 0.4
                                                                                ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200'
                                                                                : result.score > 0.38
                                                                                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200'
                                                                                    : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-200'
                                                                                }`}>
                                                                                {(result.score * 100).toFixed(1)}%
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    <p className={`text-sm ${themeClasses.text} leading-relaxed whitespace-pre-wrap`}>
                                                                        {extractTextFromApiContent(result.content)}
                                                                    </p>
                                                                </div>
                                                            ))}
                                                        </>
                                                    ) : (
                                                        <div className={`text-center py-8 ${themeClasses.textSecondary}`}>
                                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-3 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                                            </svg>
                                                            <p className="text-sm">输入查询文本开始测试</p>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </>

            {/* 删除确认对话框 */}
            {deleteConfirmModal.isOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className={`${themeClasses.cardBg} rounded-lg shadow-xl max-w-md w-full p-6`}>
                        <h3 className={`text-lg font-semibold ${themeClasses.text} mb-4`}>
                            确认删除
                        </h3>
                        <p className={`${themeClasses.textSecondary} mb-6`}>
                            确定要删除文件 "{deleteConfirmModal.file?.originalFileName}" 吗？此操作无法撤销。
                        </p>
                        <div className="flex justify-end space-x-4">
                            <button
                                onClick={cancelDelete}
                                className={`px-4 py-2 text-sm font-medium rounded-lg border ${themeClasses.border} ${themeClasses.hoverBg} ${themeClasses.text} transition-colors`}
                            >
                                取消
                            </button>
                            <button
                                onClick={confirmDelete}
                                disabled={isDeleting}
                                className={`px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:bg-red-400 disabled:cursor-not-allowed transition-colors`}
                            >
                                {isDeleting ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        删除中...
                                    </>
                                ) : (
                                    '删除'
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
});

KnowledgeBase.displayName = 'KnowledgeBase';

export default KnowledgeBase; 