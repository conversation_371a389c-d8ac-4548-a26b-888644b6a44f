import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { AppCategory, AppDetail, App, AppTool } from '../types';
import { appsApi } from '../utils/api';
import useAuth from '../hooks/useAuth';
import useSession from '../hooks/useSession';
import Modal from '../components/Modal';
import { Loading } from '../components';

interface AIAppsProps {
    isDarkMode: boolean;
    // onToggleTheme: () => void; // This will be removed
}

const defaultAvatar = 'https://i.meee.com.tw/WPcPTZB.png';

// 现代渐变配色方案
const modernGradients = [
    'from-orange-400 to-red-500',
    'from-blue-500 to-indigo-600',
    'from-green-400 to-emerald-600',
    'from-purple-500 to-pink-600',
    'from-yellow-400 to-orange-500',
    'from-teal-400 to-cyan-600',
    'from-indigo-500 to-purple-600',
    'from-pink-400 to-rose-600',
];

const getModernGradient = (id: number) => {
    return modernGradients[id % modernGradients.length];
};

const AIApps: React.FC<AIAppsProps> = React.memo(({ isDarkMode }) => {
    const { token } = useAuth();
    const navigate = useNavigate();
    const { createNewSession } = useSession();
    const [categories, setCategories] = useState<AppCategory[]>([]);
    const [selectedApp, setSelectedApp] = useState<AppDetail | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isCreatingChat, setIsCreatingChat] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<string>('精选推荐');

    useEffect(() => {
        const fetchCategories = async () => {
            try {
                setIsLoading(true);
                const response = await appsApi.getAppCategories(token || undefined);
                if (response.code === 200) {
                    // 验证和清理数据
                    const validCategories = Array.isArray(response.data)
                        ? response.data.filter(category =>
                            category &&
                            category.name &&
                            Array.isArray(category.apps)
                        ).map(category => ({
                            ...category,
                            apps: category.apps.filter(app => app && app.id)
                        }))
                        : [];
                    setCategories(validCategories);
                } else {
                    setError(response.message || '获取应用列表失败');
                }
            } catch (err: any) {
                setError(err.message || '获取应用列表时发生未知错误');
            } finally {
                setIsLoading(false);
            }
        };

        if (token) {
            fetchCategories();
        }
    }, [token]);

    const handleAppClick = useCallback(async (app: App) => {
        try {
            const response = await appsApi.getAppById(app.id, token || undefined);
            if (response.code === 200) {
                setSelectedApp(response.data);
                setIsModalOpen(true);
            } else {
                console.error(response.message);
                setError(response.message || `获取应用 ${app.name} 详情失败`);
            }
        } catch (err: any) {
            console.error(err);
            setError(err.message || `获取应用 ${app.name} 详情时发生未知错误`);
        }
    }, [token]);

    const closeModal = useCallback(() => {
        setIsModalOpen(false);
        setSelectedApp(null);
    }, []);

    const handleStartChat = useCallback(async (app: AppDetail) => {
        if (!app) return;
        setIsCreatingChat(true);
        setError(null);
        try {
            const metadata = {
                app: { id: app.id, name: app.name },
                settings: app.settings,
            };

            const newSession = await createNewSession({
                sessionTitle: `新建对话`,
                modelUsed: app.settings.model,
                metadata: JSON.stringify(metadata),
            });

            if (newSession?.sessionId) {
                closeModal();
                navigate(`/chat/${newSession.sessionId}`);
            } else {
                setError('创建会话失败，未返回有效的会话ID');
            }
        } catch (err: any) {
            console.error('创建新会话失败:', err);
            setError(err.message || '创建新会话时发生未知错误');
        } finally {
            setIsCreatingChat(false);
        }
    }, [createNewSession, closeModal, navigate]);

    // 使用 useMemo 缓存计算结果
    const { allApps, filteredApps, featuredApps } = useMemo(() => {
        const apps = categories.flatMap(category => category.apps || []).filter(app => app && app.id);

        let filtered = apps;

        // 搜索过滤
        if (searchTerm) {
            filtered = filtered.filter(app =>
                app && app.name && app.description &&
                (app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    app.description.toLowerCase().includes(searchTerm.toLowerCase()))
            );
        }

        // 分类过滤
        if (selectedCategory !== '精选推荐') {
            const category = categories.find(cat => cat.name === selectedCategory);
            if (category && category.apps) {
                filtered = category.apps.filter(app => app && app.id);
            }
        }

        return {
            allApps: apps,
            filteredApps: filtered,
            featuredApps: filtered.slice(0, 12)
        };
    }, [categories, searchTerm, selectedCategory]);

    // 热门应用 - 暂时没有热门排行数据，设为空数组
    const trendingApps: App[] = [];

    const themeClasses = useMemo(() => ({
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-gray-100' : 'text-gray-900',
        textSecondary: isDarkMode ? 'text-gray-400' : 'text-gray-600',
        textMuted: isDarkMode ? 'text-gray-500' : 'text-gray-500',
        cardBg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        cardBorder: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        inputBg: isDarkMode ? 'bg-gray-800' : 'bg-gray-50',
        searchBg: isDarkMode ? 'bg-gray-800' : 'bg-gray-100',
        modalBg: isDarkMode ? 'bg-gray-800' : 'bg-white',
        categoryBg: isDarkMode ? 'bg-gray-700' : 'bg-gray-100',
        activeCategoryBg: isDarkMode ? 'bg-gray-600' : 'bg-gray-900',
        activeCategoryText: 'text-white',
        hoverBg: isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50',
    }), [isDarkMode]);

    if (isLoading) {
        return (
            <div className={`min-h-screen ${themeClasses.background} flex items-center justify-center`}>
                <div className="text-center space-y-4">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className={themeClasses.textSecondary}>正在加载应用中心...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`min-h-screen ${themeClasses.background} flex items-center justify-center`}>
                <div className="text-center space-y-4">
                    <div className="text-6xl">😕</div>
                    <h3 className="text-xl font-semibold text-red-500">出现错误</h3>
                    <p className={themeClasses.textSecondary}>{error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                    >
                        重新加载
                    </button>
                </div>
            </div>
        );
    }

    // 分类标签数据
    const categoryTabs = [
        '精选推荐',
        ...categories.map(cat => cat.name)
    ];

    return (
        <div className={`min-h-screen ${themeClasses.background} transition-all duration-300`}>
            {/* 头部区域 - 缩小尺寸 */}
            <div className="mx-auto px-4 sm:px-6 lg:px-8 pt-8 pb-6">
                {/* 主题切换按钮 - REMOVED */}
                {/* <div className="flex justify-end mb-6">
                    <button
                        onClick={onToggleTheme}
                        className={`p-2 rounded-lg ${themeClasses.hoverBg} transition-colors`}
                    >
                        {isDarkMode ? '🌞' : '🌙'}
                    </button>
                </div> */}

                {/* 主标题 - 缩小 */}
                <div className="mb-8">
                    <h1 className={`text-3xl font-bold mb-3 ${themeClasses.text}`}>
                        AI 应用
                    </h1>
                    <p className={`text-base ${themeClasses.textSecondary} max-w-2xl`}>
                        发现和创建结合指令、额外知识和任意技能组合的定制 AI 助手版本。
                    </p>
                </div>

                {/* 搜索框 - 缩小 */}
                <div className="max-w-md mb-6">
                    <div className="relative">
                        <svg
                            className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 ${themeClasses.textMuted}`}
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input
                            type="text"
                            placeholder="搜索 AI 应用"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className={`w-full pl-9 pr-4 py-2 text-sm rounded-lg border ${themeClasses.cardBorder} ${themeClasses.searchBg} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all`}
                        />
                    </div>
                </div>

                {/* 分类标签 - 缩小 */}
                <div className="flex items-center justify-start mb-8">
                    <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-full p-1 overflow-x-auto">
                        {categoryTabs.map((category) => (
                            <button
                                key={category}
                                onClick={() => setSelectedCategory(category)}
                                className={`px-4 py-1.5 text-sm rounded-full font-medium whitespace-nowrap transition-all duration-200 ${selectedCategory === category
                                    ? `${themeClasses.activeCategoryBg} ${themeClasses.activeCategoryText}`
                                    : `${themeClasses.textSecondary} hover:bg-white dark:hover:bg-gray-700`
                                    }`}
                            >
                                {category}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            {/* 内容区域 */}
            <div className="mx-auto px-4 sm:px-6 lg:px-8 pb-12">
                {/* 精选区域 - 小卡片样式 */}
                <div className="mb-16">
                    <div className="mb-6">
                        <h2 className={`text-xl font-bold ${themeClasses.text} mb-1`}>精选推荐</h2>
                        <p className={`${themeClasses.textSecondary} text-sm`}>本周精选的顶级应用</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                        {featuredApps.filter(app => app && app.id).map((app) => (
                            <div
                                key={app.id}
                                onClick={() => handleAppClick(app)}
                                className={`flex items-center space-x-3 p-3 rounded-xl ${themeClasses.hoverBg} cursor-pointer transition-all duration-200`}
                            >
                                {/* 应用图标 */}
                                <img
                                    src={app.avatar || defaultAvatar}
                                    alt={app.name || 'AI应用'}
                                    className="w-10 h-10 rounded-lg object-cover bg-gray-200 dark:bg-gray-700 flex-shrink-0"
                                />

                                {/* 应用信息 */}
                                <div className="flex-1 min-w-0">
                                    <h3 className={`font-semibold ${themeClasses.text} truncate text-sm`}>
                                        {app.name || '未命名应用'}
                                    </h3>
                                    <p className={`${themeClasses.textSecondary} text-xs truncate`}>
                                        {app.description || '暂无描述'}
                                    </p>
                                    <p className={`${themeClasses.textMuted} text-xs`}>
                                        由 AI助手 提供
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* 热门排行榜 */}
                <div>
                    <div className="mb-6">
                        <h2 className={`text-xl font-bold ${themeClasses.text} mb-1`}>热门排行</h2>
                        <p className={`${themeClasses.textSecondary} text-sm`}>社区最受欢迎的应用</p>
                    </div>

                    {trendingApps.length > 0 ? (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                                {trendingApps.filter(app => app && app.id).map((app, index) => (
                                    <div
                                        key={app.id}
                                        onClick={() => handleAppClick(app)}
                                        className={`flex items-center space-x-3 p-3 rounded-xl ${themeClasses.hoverBg} cursor-pointer transition-all duration-200`}
                                    >
                                        {/* 排名数字 */}
                                        <div className={`w-6 h-6 flex items-center justify-center font-bold text-sm ${themeClasses.textSecondary}`}>
                                            {index + 1}
                                        </div>

                                        {/* 应用图标 */}
                                        <img
                                            src={app.avatar || defaultAvatar}
                                            alt={app.name || 'AI应用'}
                                            className="w-10 h-10 rounded-lg object-cover bg-gray-200 dark:bg-gray-700 flex-shrink-0"
                                        />

                                        {/* 应用信息 */}
                                        <div className="flex-1 min-w-0">
                                            <h3 className={`font-semibold ${themeClasses.text} truncate text-sm`}>
                                                {app.name || '未命名应用'}
                                            </h3>
                                            <p className={`${themeClasses.textSecondary} text-xs truncate`}>
                                                {app.description || '暂无描述'}
                                            </p>
                                            <p className={`${themeClasses.textMuted} text-xs`}>
                                                由 AI助手 提供
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {/* 查看更多按钮 */}
                            <div className="text-center mt-6">
                                <button className={`${themeClasses.textSecondary} hover:${themeClasses.text} font-medium transition-colors text-sm`}>
                                    查看更多
                                </button>
                            </div>
                        </>
                    ) : (
                        /* 热门排行空状态 */
                        <div className="text-center py-12">
                            <div className="text-4xl mb-3">🌟</div>
                            <h3 className={`text-lg font-semibold mb-1 ${themeClasses.text}`}>暂无热门应用</h3>
                            <p className={`${themeClasses.textSecondary} text-sm`}>精彩内容即将到来</p>
                        </div>
                    )}
                </div>

                {/* 搜索空状态 */}
                {filteredApps.length === 0 && searchTerm && (
                    <div className="text-center py-20">
                        <div className="text-8xl mb-4">🔍</div>
                        <h3 className={`text-xl font-semibold mb-2 ${themeClasses.text}`}>没有找到相关应用</h3>
                        <p className={themeClasses.textSecondary}>尝试调整搜索关键词或选择其他分类</p>
                        <button
                            onClick={() => { setSearchTerm(''); setSelectedCategory('精选推荐'); }}
                            className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                        >
                            重置搜索
                        </button>
                    </div>
                )}
            </div>

            {/* 应用详情弹窗 - 重新设计 */}
            {isModalOpen && (
                <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
                    {/* 背景模糊遮罩 */}
                    <div
                        className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity"
                        onClick={closeModal}
                    ></div>

                    {/* 弹窗内容 */}
                    <div className="relative w-full max-w-lg">
                        <div className={`relative ${themeClasses.modalBg} rounded-3xl shadow-2xl max-h-[90vh] flex flex-col overflow-hidden`}>
                            {selectedApp && (
                                <>
                                    {/* 弹窗头部 */}
                                    <div className="flex-shrink-0 p-4 border-b border-gray-100 dark:border-gray-700">
                                        <div className="flex justify-between items-start">
                                            <div className="flex-1"></div>
                                            <button
                                                onClick={closeModal}
                                                className={`p-2 rounded-lg ${themeClasses.hoverBg} transition-colors`}
                                            >
                                                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    {/* 弹窗内容区域 */}
                                    <div className="flex-1 overflow-y-auto p-6">
                                        {/* 应用头部信息 */}
                                        <div className="text-center mb-6">
                                            <img
                                                src={selectedApp.avatar || defaultAvatar}
                                                alt={selectedApp.name}
                                                className="w-20 h-20 rounded-3xl mx-auto mb-4 object-cover bg-gray-200 dark:bg-gray-700 shadow-xl"
                                            />
                                            <h2 className={`text-2xl font-bold mb-2 ${themeClasses.text}`}>{selectedApp.name}</h2>
                                            <p className={`${themeClasses.textSecondary} mb-3 text-sm leading-relaxed`}>
                                                {selectedApp.description}
                                            </p>
                                            <p className={`${themeClasses.textMuted} text-xs`}>
                                                由 AI助手 提供
                                            </p>
                                        </div>

                                        {/* 系统提示词 */}
                                        {selectedApp.settings.systemPrompt && (
                                            <div className="mb-6">
                                                <h4 className={`font-semibold mb-3 ${themeClasses.text}`}>系统提示</h4>
                                                <div className={`p-4 rounded-xl ${themeClasses.inputBg} border ${themeClasses.cardBorder}`}>
                                                    <p className="text-sm leading-relaxed whitespace-pre-wrap">
                                                        {selectedApp.settings.systemPrompt}
                                                    </p>
                                                </div>
                                            </div>
                                        )}

                                        {/* 模型配置信息 */}
                                        <div className="mb-6">
                                            <h4 className={`font-semibold mb-3 ${themeClasses.text}`}>配置信息</h4>
                                            <div className="grid grid-cols-1 gap-3">
                                                <div className={`p-4 rounded-xl ${themeClasses.inputBg} border ${themeClasses.cardBorder}`}>
                                                    <div className="flex items-center justify-between">
                                                        <span className={`text-sm ${themeClasses.textSecondary}`}>AI 模型</span>
                                                        <span className={`text-sm font-semibold ${themeClasses.text}`}>
                                                            {selectedApp.settings.model}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className={`p-4 rounded-xl ${themeClasses.inputBg} border ${themeClasses.cardBorder}`}>
                                                    <div className="flex items-center justify-between">
                                                        <span className={`text-sm ${themeClasses.textSecondary}`}>创意度</span>
                                                        <span className={`text-sm font-semibold ${themeClasses.text}`}>
                                                            {selectedApp.settings.temperature}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* 工具列表 */}
                                        {selectedApp.tools.length > 0 && (
                                            <div className="mb-6">
                                                <h4 className={`font-semibold mb-3 ${themeClasses.text}`}>可用工具</h4>
                                                <div className="space-y-2">
                                                    {selectedApp.tools.map((tool: AppTool) => (
                                                        <div
                                                            key={tool.id}
                                                            className={`p-3 rounded-xl ${themeClasses.inputBg} border ${themeClasses.cardBorder}`}
                                                        >
                                                            <div className="flex items-center justify-between">
                                                                <span className="text-sm font-medium">{tool.toolName}</span>
                                                                <span className={`px-2 py-1 rounded-full text-xs ${isDarkMode
                                                                    ? 'bg-blue-500/20 text-blue-300'
                                                                    : 'bg-blue-100 text-blue-700'
                                                                    }`}>
                                                                    {tool.toolType}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* 底部操作按钮 */}
                                    <div className="flex-shrink-0 p-6 border-t border-gray-100 dark:border-gray-700">
                                        <button
                                            onClick={() => {
                                                if (selectedApp) {
                                                    handleStartChat(selectedApp);
                                                }
                                            }}
                                            disabled={isCreatingChat}
                                            className="w-full py-3 bg-black dark:bg-white text-white dark:text-black rounded-full font-semibold hover:bg-gray-800 dark:hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            {isCreatingChat ? '正在创建会话...' : '开始对话'}
                                        </button>
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
});

export default AIApps;