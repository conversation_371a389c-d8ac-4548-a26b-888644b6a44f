import React, { useState, useEffect, useRef } from 'react';
import Masonry, { ResponsiveMasonry } from "react-responsive-masonry";
import { Search, Heart, Inbox, Loader2 } from 'lucide-react';
import { getPublicGeneratedImages } from '../utils/api';
import { useAuth } from '../hooks';
import { AiGeneratedContent } from '../types';
import ImageViewerModal from '../components/ImageViewerModal';

export interface MediaItem {
    id: string;
    type: 'image';
    url: string;
    author: string;
    authorAvatar: string;
    likes: number;
    views: number;
    prompt?: string;
    modelUsed?: string;
    size?: string;
}

const MediaCard: React.FC<{ item: MediaItem; onClick: () => void }> = ({ item, onClick }) => {
    const aspectRatio = item.size ? item.size.replace(':', ' / ') : '1 / 1';
    return (
        <div className="relative overflow-hidden rounded-lg group cursor-pointer" onClick={onClick}>
            <img
                src={item.url}
                alt={item.prompt || item.author}
                className="w-full h-auto object-cover transition-transform duration-300 group-hover:scale-105"
                style={{ aspectRatio }}
                loading="lazy"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-0 left-0 p-3 w-full flex items-center justify-between text-white">
                    <div className="flex items-center">
                        {/* <img src={item.authorAvatar} alt={item.author} className="w-6 h-6 rounded-full mr-2" />
                        <span className="text-xs font-medium">{item.author}</span> */}
                    </div>
                    <div className="flex items-center space-x-3">
                        <button className="text-white hover:text-gray-300"><Search size={14} /></button>
                        <button className="text-white hover:text-red-500"><Heart size={14} /></button>
                        <span className="text-xs">{item.likes / 1000}k</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

interface ImagesProps {
    isDarkMode: boolean;
}

const Images: React.FC<ImagesProps> = ({ isDarkMode }) => {
    const [items, setItems] = useState<MediaItem[]>([]);
    const { token, userInfo } = useAuth();
    const [page, setPage] = useState(0);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const mainContentRef = useRef<HTMLElement>(null);
    const observer = useRef<IntersectionObserver>();
    const [selectedItemIndex, setSelectedItemIndex] = useState<number | null>(null);

    const mapAiContentToMediaItem = (content: AiGeneratedContent): MediaItem => ({
        id: content.contentId,
        type: 'image',
        url: content.ossUrl,
        author: userInfo?.actualName || `User ${content.userId}`,
        authorAvatar: userInfo?.avatar || '/ai_avatar.png',
        likes: content.likeCount,
        views: content.viewCount,
        prompt: content.prompt,
        modelUsed: content.modelUsed,
        size: content.size,
    });

    const fetchImages = async (pageNum: number) => {
        if (isLoadingMore || !hasMore || !token) return;

        setIsLoadingMore(true);
        try {
            const response = await getPublicGeneratedImages(pageNum, 10, token || undefined);
            if (response.code === 200) {
                const newItems = response.data.content.map(mapAiContentToMediaItem);
                setItems(prev => pageNum === 0 ? newItems : [...prev, ...newItems]);
                setPage(pageNum + 1);
                setHasMore(!response.data.last);
            } else {
                console.error("Failed to fetch images:", response.message);
                setHasMore(false);
            }
        } catch (error) {
            console.error("Failed to fetch images:", error);
            setHasMore(false);
        } finally {
            setIsLoadingMore(false);
        }
    };

    useEffect(() => {
        if (token) {
            setItems([]);
            setPage(0);
            setHasMore(true);
            fetchImages(0);
        }
    }, [token]);

    const lastItemRef = React.useCallback((node: HTMLElement | null) => {
        if (isLoadingMore) return;
        if (observer.current) observer.current.disconnect();
        observer.current = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting && hasMore) {
                fetchImages(page);
            }
        });
        if (node) observer.current.observe(node);
    }, [isLoadingMore, hasMore, page, token]);

    useEffect(() => {
        if (isLoadingMore || !hasMore) {
            return;
        }

        const mainEl = mainContentRef.current;
        if (mainEl && mainEl.scrollHeight <= mainEl.clientHeight) {
            fetchImages(page);
        }
    }, [items, isLoadingMore, hasMore, page, token]);

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-800',
        inputBackground: isDarkMode ? 'bg-gray-800' : 'bg-gray-100',
        button: isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
    };

    return (
        <div className={`flex flex-col w-full h-screen ${themeClasses.background} ${themeClasses.text}`}>
            {/* Header */}
            <header className={`flex-shrink-0 z-10 flex items-center justify-between p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}
                style={{ backgroundColor: isDarkMode ? 'rgba(17, 24, 39, 0.8)' : 'rgba(255, 255, 255, 0.8)' }}>
                <div className="flex items-center space-x-3">
                    <h1 className="text-xl font-bold">Images</h1>
                    <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        这是一些示例的生成，可以点击查看图片的提示词
                    </span>
                </div>
                <div className="flex items-center space-x-2">
                    {/* Add filter/sort buttons here if needed */}
                </div>
            </header>

            {/* Masonry Grid */}
            <main ref={mainContentRef} className="flex-grow p-4 overflow-y-auto">
                {items.length > 0 ? (
                    <>
                        <ResponsiveMasonry columnsCountBreakPoints={{ 450: 1, 750: 2, 900: 3 }}>
                            <Masonry gutter="1rem">
                                {items.map((item, index) => {
                                    const card = <MediaCard item={item} onClick={() => setSelectedItemIndex(index)} />;
                                    if (items.length === index + 1) {
                                        return <div ref={lastItemRef} key={item.id}>{card}</div>
                                    }
                                    return <div key={item.id}>{card}</div>
                                })}
                            </Masonry>
                        </ResponsiveMasonry>
                        {isLoadingMore && (
                            <div className="flex justify-center items-center p-4">
                                <Loader2 size={24} className="animate-spin" />
                                <span className="ml-2">Loading more...</span>
                            </div>
                        )}
                    </>
                ) : (
                    <div className="flex flex-col items-center justify-center h-full text-center">
                        {isLoadingMore ? (
                            <div className="flex justify-center items-center p-4">
                                <Loader2 size={32} className="animate-spin" />
                                <span className="ml-4 text-lg">Loading images...</span>
                            </div>
                        ) : (
                            <div className={`text-center`}>
                                <Inbox size={64} className={`mx-auto mb-4 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`} />
                                <h2 className={`text-2xl font-semibold mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-700'}`}>暂无公开图片</h2>
                                <p className={`${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                                    当前还没有公开的图片内容。
                                </p>
                            </div>
                        )}
                    </div>
                )}
            </main>

            {selectedItemIndex !== null && (
                <ImageViewerModal
                    items={items}
                    currentIndex={selectedItemIndex}
                    isDarkMode={isDarkMode}
                    onClose={() => setSelectedItemIndex(null)}
                    onNavigate={(newIndex) => setSelectedItemIndex(newIndex)}
                />
            )}
        </div>
    );
};

export default Images; 