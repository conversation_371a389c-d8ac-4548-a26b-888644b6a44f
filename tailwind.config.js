/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        "./src/**/*.{js,jsx,ts,tsx}",
    ],
    theme: {
        extend: {
            animation: {
                'slideInUp': 'slideInUp 0.5s ease-in-out',
                'slideInLeft': 'slideInLeft 0.5s ease-in-out',
            },
            keyframes: {
                slideInUp: {
                    '0%': { transform: 'translateY(20px)', opacity: 0 },
                    '100%': { transform: 'translateY(0)', opacity: 1 },
                },
                slideInLeft: {
                    '0%': { transform: 'translateX(-20px)', opacity: 0 },
                    '100%': { transform: 'translateX(0)', opacity: 1 },
                }
            },
            fontFamily: {
                'sans': [
                    // 系统字体优先
                    '-apple-system',
                    'BlinkMacSystemFont',
                    // 苹果平方字体（macOS/iOS）
                    'PingFang SC',
                    'PingFang TC',
                    'PingFang HK',
                    // 其他中文字体
                    'Hiragino Sans GB',
                    'Hiragino Kaku Gothic ProN',
                    'Microsoft YaHei',
                    'Microsoft JhengHei',
                    'Source Han Sans SC',
                    'Noto Sans CJK SC',
                    'WenQuanYi Micro Hei',
                    // 西文字体后备
                    'Segoe UI',
                    'Roboto',
                    'Helvetica Neue',
                    'Arial',
                    'sans-serif'
                ],
                'system': [
                    '-apple-system',
                    'BlinkMacSystemFont',
                    'Segoe UI',
                    'Roboto',
                    'Helvetica Neue',
                    'Arial',
                    'sans-serif'
                ],
                'chinese': [
                    'PingFang SC',
                    'PingFang TC',
                    'Hiragino Sans GB',
                    'Microsoft YaHei',
                    'Source Han Sans SC',
                    'Noto Sans CJK SC',
                    'WenQuanYi Micro Hei',
                    'sans-serif'
                ]
            }
        },
    },
    plugins: [
        require('@tailwindcss/typography'),
    ],
} 