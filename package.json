{"name": "kumhosunny-app-ai", "version": "0.1.0", "private": true, "dependencies": {"@tanstack/react-query": "^5.80.7", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/react-router-dom": "^5.3.3", "antd": "^5.26.2", "axios": "^1.10.0", "framer-motion": "^12.19.1", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "mermaid": "^11.8.1", "openai": "^5.5.1", "panzoom": "^9.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "react-responsive-masonry": "^2.7.1", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.9", "react-transition-group": "^4.4.5", "remark-gfm": "^4.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "x-data-spreadsheet": "^1.1.9", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/react-pdf": "^7.0.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-transition-group": "^4.4.12", "autoprefixer": "^10.4.16", "http-proxy-middleware": "^3.0.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}}